// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: eventcategories.sql

package eventcategoriesqueries

import (
	"context"
)

type AddEventCategoriesParams struct {
	EventID    int64
	CategoryID int64
}

const deleteEventCategories = `-- name: DeleteEventCategories :exec
DELETE FROM event_categories WHERE event_id = $1
`

func (q *Queries) DeleteEventCategories(ctx context.Context, eventID int64) error {
	_, err := q.db.Exec(ctx, deleteEventCategories, eventID)
	return err
}
