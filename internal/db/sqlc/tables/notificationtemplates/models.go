// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package notificationtemplatesqueries

import (
	"database/sql/driver"
	"fmt"
	"time"
)

type Language string

const (
	LanguageEnUs Language = "en-us"
	LanguageJaJp Language = "ja-jp"
)

func (e *Language) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = Language(s)
	case string:
		*e = Language(s)
	default:
		return fmt.Errorf("unsupported scan type for Language: %T", src)
	}
	return nil
}

type NullLanguage struct {
	Language Language
	Valid    bool // Valid is true if Language is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullLanguage) Scan(value interface{}) error {
	if value == nil {
		ns.Language, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.Language.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullLanguage) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.Language), nil
}

type NotificationSeverity string

const (
	NotificationSeverityInfo    NotificationSeverity = "info"
	NotificationSeverityWarning NotificationSeverity = "warning"
	NotificationSeveritySuccess NotificationSeverity = "success"
	NotificationSeverityDanger  NotificationSeverity = "danger"
)

func (e *NotificationSeverity) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = NotificationSeverity(s)
	case string:
		*e = NotificationSeverity(s)
	default:
		return fmt.Errorf("unsupported scan type for NotificationSeverity: %T", src)
	}
	return nil
}

type NullNotificationSeverity struct {
	NotificationSeverity NotificationSeverity
	Valid                bool // Valid is true if NotificationSeverity is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullNotificationSeverity) Scan(value interface{}) error {
	if value == nil {
		ns.NotificationSeverity, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.NotificationSeverity.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullNotificationSeverity) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.NotificationSeverity), nil
}

type NotificationTemplate struct {
	Title       string
	Description string
	Json        *string
	Deeplink    *string
	Severity    NotificationSeverity
	Key         string
	Language    Language
	CreatedAt   time.Time
	UpdatedAt   time.Time
}
