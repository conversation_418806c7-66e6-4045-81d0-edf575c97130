// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: vtuberrequests.sql

package vtuberrequestsqueries

import (
	"context"
	"time"
)

const applyForVtuberAccess = `-- name: ApplyForVtuberAccess :exec
INSERT INTO vtuber_requests (description, user_id) VALUES ($1, $2)
`

type ApplyForVtuberAccessParams struct {
	Description string
	UserID      int64
}

func (q *Queries) ApplyForVtuberAccess(ctx context.Context, arg ApplyForVtuberAccessParams) error {
	_, err := q.db.Exec(ctx, applyForVtuberAccess, arg.Description, arg.UserID)
	return err
}

const approveCreatorRequest = `-- name: ApproveCreatorRequest :exec
UPDATE vtuber_requests SET status = 'approved' WHERE id = $1
`

func (q *Queries) ApproveCreatorRequest(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, approveCreatorRequest, id)
	return err
}

const denyCreatorRequest = `-- name: DenyCreatorRequest :exec
UPDATE vtuber_requests SET status = 'rejected', reason = $1 WHERE id = $2
`

type DenyCreatorRequestParams struct {
	Reason *string
	ID     int64
}

func (q *Queries) DenyCreatorRequest(ctx context.Context, arg DenyCreatorRequestParams) error {
	_, err := q.db.Exec(ctx, denyCreatorRequest, arg.Reason, arg.ID)
	return err
}

const getAllCreatorRequests = `-- name: GetAllCreatorRequests :many
WITH FILTERED AS (SELECT cr.id, cr.description, cr.status, cr.reason, cr.user_id, cr.created_at, cr.updated_at, u.full_name as name
   FROM vtuber_requests cr INNER JOIN users u ON u.id = cr.user_id),
   COUNTED AS(
      SELECT COUNT(*) AS total FROM FILTERED
   )
   SELECT f.id, f.description, f.status, f.reason, f.user_id, f.created_at, f.updated_at, f.name, c.total FROM FILTERED f, COUNTED c
   ORDER BY
      CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'DESC' THEN id END DESC,
      CASE WHEN $1::TEXT = 'status' AND $2::TEXT = 'ASC' THEN status END ASC,
      CASE WHEN $1::TEXT = 'status' AND $2::TEXT = 'DESC' THEN status END DESC,
      CASE WHEN $1::TEXT = 'reason' AND $2::TEXT = 'ASC' THEN reason END ASC,
      CASE WHEN $1::TEXT = 'reason' AND $2::TEXT = 'DESC' THEN reason END DESC,
      CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END ASC,
      CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC,
      CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN updated_at END ASC,
      CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN updated_at END DESC
   LIMIT $4 OFFSET $3
`

type GetAllCreatorRequestsParams struct {
	Sort   string
	Order  string
	Offset int32
	Limit  int32
}

type GetAllCreatorRequestsRow struct {
	ID          int64
	Description string
	Status      VtuberAccessStatus
	Reason      *string
	UserID      int64
	CreatedAt   time.Time
	UpdatedAt   time.Time
	Name        string
	Total       int64
}

func (q *Queries) GetAllCreatorRequests(ctx context.Context, arg GetAllCreatorRequestsParams) ([]GetAllCreatorRequestsRow, error) {
	rows, err := q.db.Query(ctx, getAllCreatorRequests,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllCreatorRequestsRow
	for rows.Next() {
		var i GetAllCreatorRequestsRow
		if err := rows.Scan(
			&i.ID,
			&i.Description,
			&i.Status,
			&i.Reason,
			&i.UserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Name,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCreatorRequestByID = `-- name: GetCreatorRequestByID :one
SELECT id, description, status, reason, user_id, created_at, updated_at FROM vtuber_requests WHERE id = $1
`

func (q *Queries) GetCreatorRequestByID(ctx context.Context, id int64) (VtuberRequest, error) {
	row := q.db.QueryRow(ctx, getCreatorRequestByID, id)
	var i VtuberRequest
	err := row.Scan(
		&i.ID,
		&i.Description,
		&i.Status,
		&i.Reason,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getCreatorRequestByUserID = `-- name: GetCreatorRequestByUserID :one
SELECT id, description, status, reason, user_id, created_at, updated_at FROM vtuber_requests WHERE user_id = $1
`

func (q *Queries) GetCreatorRequestByUserID(ctx context.Context, userID int64) (VtuberRequest, error) {
	row := q.db.QueryRow(ctx, getCreatorRequestByUserID, userID)
	var i VtuberRequest
	err := row.Scan(
		&i.ID,
		&i.Description,
		&i.Status,
		&i.Reason,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getCreatorRequestByVtuberId = `-- name: GetCreatorRequestByVtuberId :one
SELECT c.id, c.description, status, reason, c.user_id, c.created_at, c.updated_at, v.id, v.user_id, display_name, username, furigana, image, banner_image, social_media_links, v.description, v.created_at, v.updated_at, deleted_at FROM vtuber_requests c
INNER JOIN vtuber_profiles v 
ON v.user_id = c.user_id 
WHERE v.id = $1 AND
v.deleted_at IS NULL
`

type GetCreatorRequestByVtuberIdRow struct {
	ID               int64
	Description      string
	Status           VtuberAccessStatus
	Reason           *string
	UserID           int64
	CreatedAt        time.Time
	UpdatedAt        time.Time
	ID_2             int64
	UserID_2         int64
	DisplayName      string
	Username         string
	Furigana         string
	Image            *string
	BannerImage      *string
	SocialMediaLinks []byte
	Description_2    *string
	CreatedAt_2      time.Time
	UpdatedAt_2      time.Time
	DeletedAt        *time.Time
}

func (q *Queries) GetCreatorRequestByVtuberId(ctx context.Context, id int64) (GetCreatorRequestByVtuberIdRow, error) {
	row := q.db.QueryRow(ctx, getCreatorRequestByVtuberId, id)
	var i GetCreatorRequestByVtuberIdRow
	err := row.Scan(
		&i.ID,
		&i.Description,
		&i.Status,
		&i.Reason,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ID_2,
		&i.UserID_2,
		&i.DisplayName,
		&i.Username,
		&i.Furigana,
		&i.Image,
		&i.BannerImage,
		&i.SocialMediaLinks,
		&i.Description_2,
		&i.CreatedAt_2,
		&i.UpdatedAt_2,
		&i.DeletedAt,
	)
	return i, err
}

const updateCreatorRequest = `-- name: UpdateCreatorRequest :exec
UPDATE vtuber_requests SET status = 'pending' , reason = NULL , description = $1 WHERE id = $2
`

type UpdateCreatorRequestParams struct {
	Description string
	ID          int64
}

func (q *Queries) UpdateCreatorRequest(ctx context.Context, arg UpdateCreatorRequestParams) error {
	_, err := q.db.Exec(ctx, updateCreatorRequest, arg.Description, arg.ID)
	return err
}
