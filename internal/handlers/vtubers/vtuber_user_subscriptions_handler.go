package vtubers

import (
	"context"
	"errors"
	"strconv"
	"time"

	transactionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/transactions"
	userbillinginfosqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userbillinginfos"
	userpointsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userpoints"
	vtuberplansqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberplans"
	vtuberusersubscriptionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberusersubscriptions"
	"github.com/nsp-inc/vtuber/internal/domain/billing"

	"github.com/nsp-inc/vtuber/packages/gmo"

	"connectrpc.com/connect"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	vtubersv1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
)

type VtuberUserSubscriptionService struct {
	repo            *vtuberusersubscriptionsqueries.Queries
	vtuberPlanRepo  *vtuberplansqueries.Queries
	userBillingRepo *userbillinginfosqueries.Queries
	userPointRepo   *userpointsqueries.Queries
	transactionRepo *transactionsqueries.Queries
}

func NewVtuberUserSubscriptionService(vtuberUserSubRepo *vtuberusersubscriptionsqueries.Queries,
	vtuberPlanRepo *vtuberplansqueries.Queries,
	userBillingRepo *userbillinginfosqueries.Queries,
	userPointRepo *userpointsqueries.Queries,
	transactionRepo *transactionsqueries.Queries) *VtuberUserSubscriptionService {
	return &VtuberUserSubscriptionService{
		repo:            vtuberUserSubRepo,
		vtuberPlanRepo:  vtuberPlanRepo,
		userBillingRepo: userBillingRepo,
		userPointRepo:   userPointRepo,
		transactionRepo: transactionRepo,
	}
}

func (s *VtuberUserSubscriptionService) AddVtuberUserSubscription(ctx context.Context, req *connect.Request[vtubersv1.AddVtuberUserSubscriptionRequest]) (*connect.Response[vtubersv1.AddVtuberUserSubscriptionResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	vtuberPlan, err := s.vtuberPlanRepo.FindVtuberPlanById(ctx, req.Msg.VtuberPlanId)
	if err != nil {
		return nil, err
	}
	billingInfo, err := s.userBillingRepo.GetBillingInfoById(ctx, req.Msg.BillingId)
	if err != nil {
		return nil, err
	}

	if billingInfo.UserID != sessionUser.ID {
		return nil, errors.New("invalid billing info")
	}

	runningSub, err := s.repo.CheckRunningSubscription(ctx, vtuberusersubscriptionsqueries.CheckRunningSubscriptionParams{
		VtuberID: vtuberPlan.VtuberID,
		UserID:   sessionUser.ID,
	})
	hasRunningSub := err == nil && runningSub.ID != 0
	var expiresOn time.Time
	var price int32

	if req.Msg.PlanType == "annual" {
		expiresOn = time.Now().Add(time.Hour * 24 * 365)
		price = vtuberPlan.AnnualPrice
	} else {
		expiresOn = time.Now().Add(time.Hour * 24 * 30)
		price = vtuberPlan.Price
	}

	var vtuberUserSubscription vtuberusersubscriptionsqueries.VtuberUserSubscription

	//gmo call
	var gmoOrderId string
	for {
		gmoOrderId = gmo.GenerateGmoOrderId()
		_, err = s.transactionRepo.CheckGmoOrderIdExist(ctx, gmoOrderId)
		if err != nil {
			break
		}
	}
	createTransactionResponse, err := gmo.GmoAPICall(
		gmo.GmoApiParams{
			MemberID: billingInfo.GmoMemberID,
			OrderID:  gmoOrderId,
			Amount:   strconv.Itoa(int(price)),
			Status:   "AUTH",
			JobCd:    "AUTH",
		}, gmo.EntryTranEndPoint)

	if err != nil {
		return nil, err
	}

	_, err = gmo.GmoAPICall(
		gmo.GmoApiParams{
			MemberID:   billingInfo.GmoMemberID,
			OrderID:    gmoOrderId,
			AccessID:   createTransactionResponse.AccessID,
			AccessPass: createTransactionResponse.AccessPass,
			Method:     "1",
			SeqMode:    "0",
			CardSeq:    "0",
		}, gmo.ExecTranEndPoint)

	if err != nil {
		return nil, err
	}

	_, err = gmo.GmoAPICall(
		gmo.GmoApiParams{
			Amount:     strconv.Itoa(int(price)),
			AccessID:   createTransactionResponse.AccessID,
			AccessPass: createTransactionResponse.AccessPass,
			JobCd:      "SALES",
			Tax:        "0",
			Method:     "1",
			PayTimes:   "1",
		}, gmo.AlterTranEndPoint)

	if err != nil {
		return nil, err
	}
	if !hasRunningSub {
		subscription, err := s.repo.AddVtuberUserSubscription(ctx, vtuberusersubscriptionsqueries.AddVtuberUserSubscriptionParams{
			VtuberID:     vtuberPlan.VtuberID,
			UserID:       sessionUser.ID,
			VtuberPlanID: req.Msg.VtuberPlanId,
			IsRecurring:  req.Msg.IsRecurring,
			ExpiresOn:    expiresOn,
		})
		if err != nil {
			return nil, err
		}
		vtuberUserSubscription = subscription
	} else {
		subscription, err := s.repo.UpdateIfExist(ctx, vtuberusersubscriptionsqueries.UpdateIfExistParams{
			IsRecurring:  req.Msg.IsRecurring,
			ExpiresOn:    expiresOn,
			VtuberPlanID: req.Msg.VtuberPlanId,
			ID:           runningSub.ID,
		})

		if err != nil {
			return nil, err
		}
		vtuberUserSubscription = subscription
	}

	//save transaction
	card, err := gmo.SearchCard(ctx, billingInfo.GmoMemberID)
	if err != nil {
		return nil, err
	}

	var cardNo string
	var cardExpiry string
	if card != nil {
		cardNo = card.CardNumber
		cardExpiry = card.CardExpiryDate
	} else {
		cardNo = "N/A"
		cardExpiry = "N/A"
	}

	transactionDetail, err := billing.TransactionDetail{
		VtuberUserSubscription: &billing.VtuberUserSubscriptionDetails{
			VtuberId:           vtuberPlan.VtuberID,
			Amount:             price,
			SubscriptionId:     req.Msg.VtuberPlanId,
			UserSubscriptionId: vtuberUserSubscription.ID,
			ExpiresAt:          expiresOn,
			IsRenewal:          false,
			PlanType:           req.Msg.PlanType,
		},
		BillingInfo: &billing.BillingInfo{
			CardNumber:     cardNo,
			CardExpiryDate: cardExpiry,
			FullName:       billingInfo.FullName,
			Address1:       billingInfo.Address1,
			Address2:       billingInfo.Address2,
			City:           billingInfo.City,
			State:          billingInfo.State,
			Country:        billingInfo.Country,
			PostalCode:     billingInfo.PostalCode,
			CompanyName:    billingInfo.CompanyName,
			VatNumber:      billingInfo.VatNumber,
		},
	}.ToByte()
	if err != nil {
		return nil, err
	}

	transaction, err := s.transactionRepo.AddTransaction(ctx, transactionsqueries.AddTransactionParams{
		UserID:     sessionUser.ID,
		Amount:     price,
		Type:       "transaction",
		Status:     "payment_done",
		VtuberID:   &vtuberPlan.VtuberID,
		GmoOrderID: gmoOrderId,
		Details:    transactionDetail,
	})
	if err != nil {
		return nil, err
	}

	points := float64(price) / float64(500)
	_, err = s.userPointRepo.AddUserPoints(ctx, userpointsqueries.AddUserPointsParams{
		UserID:        sessionUser.ID,
		TransactionID: &transaction.ID,
		Points:        points,
	})
	if err != nil {
		return nil, err
	}
	//todo::send notification to vtuber
	//todo::send notification to user
	//todo::send success email to user

	return connect.NewResponse(&vtubersv1.AddVtuberUserSubscriptionResponse{
		Data: &vtubersv1.VtuberUserSubscription{
			Id:                   vtuberUserSubscription.ID,
			VtuberId:             vtuberUserSubscription.VtuberID,
			UserId:               sessionUser.ID,
			VtuberSubscriptionId: req.Msg.VtuberPlanId,
			IsRecurring:          vtuberUserSubscription.IsRecurring,
			CreatedAt:            timestamppb.New(vtuberUserSubscription.CreatedAt),
			ExpiresOn:            timestamppb.New(vtuberUserSubscription.ExpiresOn),
		},
	}), nil
}

func (s *VtuberUserSubscriptionService) GetVtuberUserSubscriptionById(ctx context.Context, req *connect.Request[vtubersv1.GetVtuberUserSubscriptionByIdRequest]) (*connect.Response[vtubersv1.VtuberUserSubscription], error) {
	vtuberSub, err := s.repo.GetVtuberUserSubById(ctx, req.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "vtuberUserSub", nil),
			}),
		)
	}
	return connect.NewResponse(
		&vtubersv1.VtuberUserSubscription{
			Id:       vtuberSub.ID,
			VtuberId: vtuberSub.VtuberID,
			UserId:   vtuberSub.UserID,
			User: &vtubersv1.User{
				Id:    vtuberSub.UserID,
				Email: vtuberSub.UserEmail,
				Name:  vtuberSub.UserName,
				Image: helpers.GetCdnLinkPointer(vtuberSub.UserImage),
			},
			VtuberSubscriptionId: vtuberSub.VtuberPlanID,
			CreatedAt:            timestamppb.New(vtuberSub.CreatedAt),
			IsRecurring:          vtuberSub.IsRecurring,
			ExpiresOn:            timestamppb.New(vtuberSub.ExpiresOn),
		},
	), nil
}

func (s *VtuberUserSubscriptionService) DeleteVtuberUserSubscriptionById(ctx context.Context, req *connect.Request[vtubersv1.DeleteVtuberUserSubscriptionByIdRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	vtuberUserSub, err := s.repo.GetVtuberUserSubById(ctx, req.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "vtuberUserSub", nil),
			}),
		)
	}
	if sessionUser.ID != vtuberUserSub.UserID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedToPerformAction", nil))
	}
	err = s.repo.DeleteVtuberUserSubscription(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  200,
		Message: web.GetTranslation(ctx, "vtuberUserSubDeleted", nil),
		Success: true,
	}), nil
}

func (c *VtuberUserSubscriptionService) UpdateVtuberUserSubscriptionById(ctx context.Context, req *connect.Request[vtubersv1.UpdateVtuberUserSubscriptionRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	vtuberSub, err := c.repo.GetVtuberUserSubById(ctx, req.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "vtuberUserSub", nil),
			}),
		)
	}
	if vtuberSub.UserID != sessionUser.ID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedToPerformAction", nil))
	}
	if vtuberSub.IsRecurring == req.Msg.IsRecurring {
		return connect.NewResponse(&sharedv1.GenericResponse{
			Status:  200,
			Message: web.GetTranslation(ctx, "vtuberUserSubUpdated", nil),
			Success: true,
		}), nil
	}
	err = c.repo.UpdateVtuberUserSubscription(ctx, vtuberusersubscriptionsqueries.UpdateVtuberUserSubscriptionParams{
		IsRecurring: req.Msg.IsRecurring,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  200,
		Message: web.GetTranslation(ctx, "vtuberUserSubUpdated", nil),
		Success: true,
	}), nil
}

func (s *VtuberUserSubscriptionService) GetMyVtuberUserSubscriptions(ctx context.Context, req *connect.Request[vtubersv1.GetMyVtuberUserSubscriptionsRequest]) (*connect.Response[vtubersv1.GetAllVtuberUserSubscriptionsResponse], error) {
	pagination := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"id", "is_recurring", "expires_on", "created_at", "updated_at"})
	sessionUser := web.GetUserFromContext(ctx)
	vtuberSubs, err := s.repo.GetVtuberUserSubscription(ctx, vtuberusersubscriptionsqueries.GetVtuberUserSubscriptionParams{
		VtuberID:     sessionUser.VtuberId,
		VtuberPlanID: req.Msg.SubId,
		Limit:        pagination.PageSize,
		Offset:       pagination.Offset,
		Order:        pagination.OrderDirection,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&vtubersv1.GetAllVtuberUserSubscriptionsResponse{
		Vtubersubscription: helpers.Map(vtuberSubs, func(vtuberSub vtuberusersubscriptionsqueries.GetVtuberUserSubscriptionRow) *vtubersv1.VtuberUserSubscription {
			return &vtubersv1.VtuberUserSubscription{
				Id:       vtuberSub.ID,
				VtuberId: vtuberSub.VtuberID,
				User: &vtubersv1.User{
					Id:    vtuberSub.UserID,
					Name:  vtuberSub.UserName,
					Email: vtuberSub.UserEmail,
					Image: helpers.GetCdnLinkPointer(vtuberSub.UserImage),
				},
				UserId:               vtuberSub.UserID,
				VtuberSubscriptionId: vtuberSub.VtuberPlanID,
				CreatedAt:            timestamppb.New(vtuberSub.CreatedAt),
				IsRecurring:          vtuberSub.IsRecurring,
				ExpiresOn:            timestamppb.New(vtuberSub.ExpiresOn),
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(pagination, helpers.GetFirstElement(vtuberSubs)),
	}), nil
}

func (s *VtuberUserSubscriptionService) GetAllVtuberUserSubscriptions(ctx context.Context, req *connect.Request[vtubersv1.GetAllVtuberUserSubscriptionsRequest]) (*connect.Response[vtubersv1.GetAllVtuberUserSubscriptionsResponse], error) {
	pagination := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"id", "is_recurring", "expires_on", "created_at", "updated_at"})
	sessionUser := web.GetUserFromContext(ctx)
	vtuberSubs, err := s.repo.GetVtuberUserSubscription(ctx, vtuberusersubscriptionsqueries.GetVtuberUserSubscriptionParams{
		VtuberID:     sessionUser.VtuberId,
		VtuberPlanID: req.Msg.VtuberSubscriptionId,
		UserID:       req.Msg.UserId,
		Limit:        pagination.PageSize,
		Offset:       pagination.Offset,
		Order:        pagination.OrderDirection,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&vtubersv1.GetAllVtuberUserSubscriptionsResponse{
		Vtubersubscription: helpers.Map(vtuberSubs, func(vtuberSub vtuberusersubscriptionsqueries.GetVtuberUserSubscriptionRow) *vtubersv1.VtuberUserSubscription {
			return &vtubersv1.VtuberUserSubscription{
				Id:       vtuberSub.ID,
				VtuberId: vtuberSub.VtuberID,
				UserId:   vtuberSub.UserID,
				User: &vtubersv1.User{
					Id:    vtuberSub.UserID,
					Email: vtuberSub.UserEmail,
					Name:  vtuberSub.UserName,
					Image: helpers.GetCdnLinkPointer(vtuberSub.UserImage),
				},
				VtuberSubscriptionId: vtuberSub.VtuberPlanID,
				CreatedAt:            timestamppb.New(vtuberSub.CreatedAt),
				IsRecurring:          vtuberSub.IsRecurring,
				ExpiresOn:            timestamppb.New(vtuberSub.ExpiresOn),
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(pagination, helpers.GetFirstElement(vtuberSubs)),
	}), nil
}
