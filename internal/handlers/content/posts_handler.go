package content

import (
	"context"
	"errors"
	"strconv"

	"connectrpc.com/connect"
	contentv1 "github.com/nsp-inc/vtuber/api/content/v1"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	categoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/categories"
	postsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/posts"
	vtuberprofilesv1repo "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberprofiles"
	vtuberusersubscriptionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberusersubscriptions"
	storagev1 "github.com/nsp-inc/vtuber/internal/storage"

	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/validation"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type PostService struct {
	repo               *postsqueries.Queries
	categoryRepo       *categoriesqueries.Queries
	vtuberProfileRepo  *vtuberprofilesv1repo.Queries
	storageService     *storagev1.StorageService
	creatorUserSubRepo *vtuberusersubscriptionsqueries.Queries
}

func NewPostService(postRepo *postsqueries.Queries, categoryRepo *categoriesqueries.Queries, vtuberProfileRepo *vtuberprofilesv1repo.Queries, storageService *storagev1.StorageService, creatorUserSubRepo *vtuberusersubscriptionsqueries.Queries) *PostService {
	return &PostService{
		repo:               postRepo,
		categoryRepo:       categoryRepo,
		vtuberProfileRepo:  vtuberProfileRepo,
		storageService:     storageService,
		creatorUserSubRepo: creatorUserSubRepo,
	}
}

func (p PostService) GetMyPosts(ctx context.Context, request *connect.Request[contentv1.GetMyPostsRequest]) (*connect.Response[contentv1.GetAllPostsResponse], error) {

	sessionUser := web.GetUserFromContext(ctx)

	paginationInfo := sharedv1.GetPaginationRequestInfo(request.Msg.Pagination, []string{"name", "title", "created_at", "updated_at"})
	posts, err := p.repo.ListPosts(ctx, postsqueries.ListPostsParams{
		Limit:    paginationInfo.PageSize,
		Offset:   paginationInfo.Offset,
		Order:    paginationInfo.OrderDirection,
		Sort:     paginationInfo.OrderBy,
		VtuberID: sessionUser.VtuberId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&contentv1.GetAllPostsResponse{
		Data: helpers.Map(posts, func(post postsqueries.ListPostsRow) *contentv1.Post {
			return &contentv1.Post{
				Id: post.ID,
				Vtuber: &sharedv1.Profile{
					Id:    post.VtuberProfileID,
					Name:  post.VtuberName,
					Image: helpers.GetCdnLinkPointer(post.VtuberImage),
				},
				MembershipOnly:   post.MembershipOnly,
				Description:      post.Description,
				ShortDescription: post.ShortDescription,
				Title:            post.Title,
				Name:             post.Name,
				Media:            helpers.GetCdnLinkPointer(post.Media),
				MediaType:        post.MediaType,
				CategoryId:       post.CategoryID,
				PostLikes:        post.Likes,
				PostComments:     post.Comments,
				CreatedAt:        timestamppb.New(post.CreatedAt),
				Slug:             post.Slug,
				CampaignId:       post.CampaignID,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(posts)),
	},
	), nil
}

func (p PostService) AddPost(ctx context.Context, c *connect.Request[contentv1.AddPostRequest]) (*connect.Response[contentv1.AddPostResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	_, err := p.categoryRepo.GetCategoryById(ctx, strconv.FormatInt(c.Msg.CategoryId, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "category", nil),
			}),
		)
	}

	mediaType := storagev1.ImageFileType
	if c.Msg.MediaType != nil && *c.Msg.MediaType == "video" {
		mediaType = storagev1.VideoFileType
	}

	newPath, err := p.storageService.ValidateAndUploadFromTemp(ctx, c.Msg.Media, mediaType, "posts", false, nil)
	if err != nil {
		return nil, validation.NewFieldError("media", err)
	}

	slug := helpers.SlugifyWithTimestamp(c.Msg.Title)
	_, err = p.repo.GetPostBySlug(ctx, slug)
	if err == nil {
		slug = helpers.SlugifyWithTimestamp(c.Msg.Title)
	}

	post, err := p.repo.AddPost(ctx, postsqueries.AddPostParams{
		Title:            c.Msg.Title,
		Description:      helpers.SanitizeHtml(c.Msg.Description),
		ShortDescription: c.Msg.ShortDescription,
		CategoryID:       c.Msg.CategoryId,
		Media:            helpers.GetPointerString(newPath),
		MediaType:        c.Msg.MediaType,
		Name:             c.Msg.Name,
		MembershipOnly:   c.Msg.MembershipOnly,
		VtuberProfileID:  *sessionUser.VtuberId,
		Slug:             slug,
		CampaignID:       c.Msg.CampaignId,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&contentv1.AddPostResponse{
		Data: &contentv1.Post{
			Id: post.ID,
			Vtuber: &sharedv1.Profile{
				Id:    post.VtuberProfileID,
				Name:  post.VtuberName,
				Image: helpers.GetCdnLinkPointer(post.VtuberImage),
			},
			MembershipOnly:   post.MembershipOnly,
			Description:      post.Description,
			ShortDescription: post.ShortDescription,
			Title:            post.Title,
			Name:             post.Name,
			Media:            helpers.GetCdnLinkPointer(post.Media),
			MediaType:        post.MediaType,
			CategoryId:       post.CategoryID,
			CreatedAt:        timestamppb.New(post.CreatedAt),
			Slug:             post.Slug,
			CampaignId:       post.CampaignID,
		},
	}), nil

}

func (p PostService) GetAllPosts(ctx context.Context, c *connect.Request[contentv1.GetAllPostsRequest]) (*connect.Response[contentv1.GetAllPostsResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"name", "title", "created_at", "updated_at"})
	sessionUser := web.GetUserFromContext(ctx)
	var userId *int64

	if sessionUser != nil {
		userId = &sessionUser.ID
	}

	posts, err := p.repo.ListPosts(ctx, postsqueries.ListPostsParams{
		Limit:          paginationInfo.PageSize,
		Offset:         paginationInfo.Offset,
		Order:          paginationInfo.OrderDirection,
		Sort:           paginationInfo.OrderBy,
		VtuberID:       c.Msg.VtuberId,
		CategoryID:     c.Msg.CategoryId,
		UserID:         userId,
		VtuberUsername: c.Msg.VtuberUsername,
		CampaignID:     c.Msg.CampaignId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&contentv1.GetAllPostsResponse{
		Data: helpers.Map(posts, func(post postsqueries.ListPostsRow) *contentv1.Post {
			return &contentv1.Post{
				Id: post.ID,
				Vtuber: &sharedv1.Profile{
					Id:    post.VtuberProfileID,
					Name:  post.VtuberName,
					Image: helpers.GetCdnLinkPointer(post.VtuberImage),
				},
				MembershipOnly:   post.MembershipOnly,
				Description:      post.Description,
				ShortDescription: post.ShortDescription,
				Title:            post.Title,
				Name:             post.Name,
				Media:            helpers.GetCdnLinkPointer(post.Media),
				MediaType:        post.MediaType,
				CategoryId:       post.CategoryID,
				CreatedAt:        timestamppb.New(post.CreatedAt),
				PostLikes:        post.Likes,
				HasLiked:         post.HasLiked,
				PostComments:     post.Comments,
				Slug:             post.Slug,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(posts)),
	},
	), nil
}

func (p PostService) GetPostById(ctx context.Context, c *connect.Request[contentv1.GetPostByIdRequest]) (*connect.Response[contentv1.GetPostByIdResponse], error) {
	var userId *int64
	sessionUser := web.GetUserFromContext(ctx)
	if sessionUser != nil {
		userId = &sessionUser.ID
	}
	post, err := p.repo.GetPostByID(ctx, postsqueries.GetPostByIDParams{
		ID:     c.Msg.Id,
		UserID: userId,
	})
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "post", nil),
			}),
		)
	}

	return connect.NewResponse(&contentv1.GetPostByIdResponse{
		Data: &contentv1.Post{
			Id: post.ID,
			Vtuber: &sharedv1.Profile{
				Id:    post.VtuberProfileID,
				Name:  post.VtuberName,
				Image: helpers.GetCdnLinkPointer(post.VtuberImage),
			},
			MembershipOnly:   post.MembershipOnly,
			Description:      post.Description,
			ShortDescription: post.ShortDescription,
			Title:            post.Title,
			Name:             post.Name,
			Media:            helpers.GetCdnLinkPointer(post.Media),
			MediaType:        post.MediaType,
			CategoryId:       post.CategoryID,
			CreatedAt:        timestamppb.New(post.CreatedAt),
			HasLiked:         post.HasLiked,
			PostLikes:        post.Likes,
			PostComments:     post.Comments,
			Slug:             post.Slug,
			CampaignId:       post.CampaignID,
		},
	}), nil
}

func (p PostService) DeletePostById(ctx context.Context, c *connect.Request[contentv1.DeletePostByIdRequest]) (*connect.Response[contentv1.DeletePostByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	post, err := p.repo.GetPost(ctx, strconv.FormatInt(c.Msg.Id, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "post", nil),
			}),
		)
	}

	if *sessionUser.VtuberId != post.VtuberProfileID {
		return nil, errors.New(
			web.GetTranslation(ctx, "unauthorizedToPerformAction", nil),
		)
	}
	err = p.repo.DeletePost(ctx, c.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&contentv1.DeletePostByIdResponse{

		Message: web.GetTranslation(ctx, "postDeleted", nil),
		Success: true,
	}), nil
}

func (p PostService) UpdatePostById(ctx context.Context, c *connect.Request[contentv1.UpdatePostByIdRequest]) (*connect.Response[contentv1.UpdatePostByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	post, err := p.repo.GetPost(ctx, strconv.FormatInt(c.Msg.Id, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "post", nil),
			}),
		)
	}
	if *sessionUser.VtuberId != post.VtuberProfileID {
		return nil, errors.New(
			web.GetTranslation(ctx, "unauthorizedToPerformAction", nil),
		)
	}
	_, err = p.categoryRepo.GetCategoryById(ctx, strconv.FormatInt(c.Msg.CategoryId, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "category", nil),
			}),
		)
	}

	mediaType := storagev1.ImageFileType
	if c.Msg.MediaType != nil && *c.Msg.MediaType == "video" {
		mediaType = storagev1.VideoFileType
	}

	newPath, err := p.storageService.ValidateAndUploadFromTemp(ctx, c.Msg.Media, mediaType, "posts", false, post.Media)
	if err != nil {
		return nil, validation.NewFieldError("media", err)
	}

	if c.Msg.Media == nil {
		newPath = ""
	}
	err = p.repo.UpdatePost(ctx, postsqueries.UpdatePostParams{
		Title:            c.Msg.Title,
		Description:      c.Msg.Description,
		ShortDescription: c.Msg.ShortDescription,
		CategoryID:       c.Msg.CategoryId,
		Media:            helpers.GetPointerString(newPath),
		MediaType:        c.Msg.MediaType,
		Name:             c.Msg.Name,
		MembershipOnly:   c.Msg.MembershipOnly,
		ID:               c.Msg.Id,
		CampaignID:       c.Msg.CampaignId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&contentv1.UpdatePostByIdResponse{
		Message: web.GetTranslation(ctx, "postUpdated", nil),
		Success: true,
	}), nil
}

func (p PostService) GetVtuberGalleries(ctx context.Context, c *connect.Request[contentv1.GetVtuberGalleryRequest]) (*connect.Response[contentv1.GetAllPostsResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"created_at"})

	posts, err := p.repo.GetVtuberGalleries(ctx, postsqueries.GetVtuberGalleriesParams{
		Limit:           paginationInfo.PageSize,
		Offset:          paginationInfo.Offset,
		Order:           paginationInfo.OrderDirection,
		Sort:            paginationInfo.OrderBy,
		VtuberProfileID: c.Msg.VtuberId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&contentv1.GetAllPostsResponse{
		Data: helpers.Map(posts, func(post postsqueries.GetVtuberGalleriesRow) *contentv1.Post {
			return &contentv1.Post{
				Id:               post.ID,
				MembershipOnly:   post.MembershipOnly,
				Description:      post.Description,
				ShortDescription: post.ShortDescription,
				Title:            post.Title,
				Name:             post.Name,
				Media:            helpers.GetCdnLinkPointer(post.Media),
				MediaType:        post.MediaType,
				CategoryId:       post.CategoryID,
				CreatedAt:        timestamppb.New(post.CreatedAt),
				Slug:             post.Slug,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(posts)),
	},
	), nil
}
