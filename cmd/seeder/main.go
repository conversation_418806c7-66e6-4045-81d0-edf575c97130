package main

import (
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	"github.com/nsp-inc/vtuber/cmd/seeder/model"
	"github.com/nsp-inc/vtuber/internal/domain/users"
	"github.com/nsp-inc/vtuber/packages/env"
	"github.com/nsp-inc/vtuber/packages/hasher"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var faker = gofakeit.New(0)
var vtubers []model.User

var db *gorm.DB

func getRandomCategory() model.Category {
	return categories[faker.Number(0, len(categories)-1)]
}

func getRandomCampaigns(campaigns []model.Campaign) *model.Campaign {
	return &campaigns[faker.Number(0, len(campaigns)-1)]
}

func getRandomElement[T any](slice []T) T {
	if len(slice) == 0 {
		var zeroValue T
		return zeroValue
	}
	return slice[faker.Number(0, len(slice)-1)]
}

func getRandomNElements[T any](slice []T, n int) []T {
	if n <= 0 || len(slice) == 0 {
		return []T{}
	}
	if n > len(slice) {
		n = len(slice)
	}

	// Create a copy to avoid modifying the original slice
	shuffled := make([]T, len(slice))
	copy(shuffled, slice)

	// Shuffle the copy using Fisher-Yates algorithm
	for i := len(shuffled) - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		shuffled[i], shuffled[j] = shuffled[j], shuffled[i]
	}

	return shuffled[:n]
}

func init() {
	defaultDB := env.GetString("DB_STRING", "")

	silentMode := env.GetBool("SILENT_MODE", false)
	logMode := logger.Silent
	if !silentMode {
		logMode = logger.Info
	}
	// Connect to the default `postgres` database
	conn, err := gorm.Open(postgres.Open(defaultDB), &gorm.Config{
		SkipDefaultTransaction: true,
		Logger:                 logger.Default.LogMode(logMode),
	})
	if err != nil {
		panic("Failed to connect to default database: " + err.Error())
	}
	db = conn
}

func getRandomImage() string {
	return fmt.Sprintf("https://cdn.v-sai.com/dummy/%d.jpg", faker.Number(1, 204))
}

func generateSlice[T any](fn func(i int) (T, error), size int) []T {
	slice := make([]T, size)
	for i := range size {
		item, err := fn(i)
		if err != nil {
			continue
		}
		slice[i] = item
	}
	return slice
}

var categories []model.Category

func main() {
	seedCategories()
	seedAdmin()
	seedUsers()
	seedStaticData()
	seedFaqs()
	seedAnnouncement()
}

func seedAnnouncement() {
	announcements := generateSlice(func(i int) (model.Announcement, error) {
		return model.Announcement{
			Image:       getRandomImage(),
			Description: faker.Paragraph(2, 5, 5, "\n"),
			Active:      true,
		}, nil
	}, 1)
	err := db.Create(&announcements).Error
	if err != nil {
		panic(err)
	}

}

func seedAdmin() {
	password, err := hasher.Hash("password")
	if err != nil {
		panic(err)
	}
	passwords := users.LastThreePasswords{}

	passwords.AddPassword(password)

	maxDob := time.Now().Add(-24 * 365 * 18 * time.Hour) // 18 years ago
	minDob := time.Now().Add(-24 * 365 * 30 * time.Hour) // 30 years ago

	admin := model.User{
		FullName:      "Admin User",
		Email:         "<EMAIL>",
		EmailVerified: true,
		DateOfBirth:   faker.DateRange(minDob, maxDob),
		Image:         getRandomImage(),
		Role:          "admin",
		Accounts: []model.Account{
			{
				ProviderID:        "email",
				Password:          password,
				LastThreePassword: string(passwords.ToByte()),
			},
		},
	}
	db.Create(&admin)
}

func seedCategories() {
	categories = generateSlice(func(i int) (model.Category, error) {
		var name = faker.BookGenre()
		return model.Category{
			Name:        name,
			Image:       getRandomImage(),
			Description: faker.Paragraph(2, 10, 10, "\n"),
			Slug:        helpers.SlugifyWithTimestamp(name),
		}, nil
	}, 12)
	err := db.Create(&categories).Error
	if err != nil {
		panic(err)
	}

}

func seedStaticData() {
	var staticData = []model.StaticDatum{

		{
			Key:      "privacy-policy",
			Value:    `<h1 class="scroll-m-20 text-4xl text-primary my-6 font-extrabold tracking-tight lg:text-5xl" dir="ltr" style="text-align: center;"><b><strong class="font-bold" style="white-space: pre-wrap;">プライバシーポリシー</strong></b></h1><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><br><span style="white-space: pre-wrap;">デジタルギア株式会社（以下「当社」といいます。）の運営する「V祭（ぶいさい）」（以下「本サービス」といいます。）は、以下のプライバシーポリシーに基づき、適正に個人情報を収集し取り扱います。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報収集について</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、ユーザーに関する次の各号に掲げる個人情報を取得します。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（1）本人に関する情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">氏名、住所、郵便番号、性別、生年月日、電話番号、メールアドレス、会員ID、パスワード、外部SNSサービスのアカウント情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（2）本サービスの利用に関する情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">本サービスの申込内容、購入履歴に関する情報等</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（3）決済に関する情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">金融機関口座に関する情報、決済及びその方法に関する情報等</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（4）ブラウザ上で取得する情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">Cookie等のオンライン上の識別子、端末情報、位置情報、閲覧履歴、IPアドレスの情報等</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（5）お問い合わせに関する情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">ユーザーから当社へのお問い合わせ・ご連絡に関する情報等</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0"><b><strong class="font-bold" style="white-space: pre-wrap;">&nbsp;</strong></b></h2><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の利用について</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、本サービスを通して収集したユーザーの個⼈情報を当社の事業運営のために次の各号に定める⽬的で利⽤することができるものとします。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（1）本人確認、本人認証のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（2）アフターサービス、お問い合わせ、苦情対応のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（3）当社及び本サービスへのお問い合わせ、本サービスの運営上必要な事項の通知（電⼦メールによるものを含むものとします。）のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（4）ユーザーが投稿した情報の掲載のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（5）システムの維持、不具合対応のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（6）メールマガジン、ダイレクトメール等の広告物送信のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（7）当社のサービス・商品等に関するご案内（セミナー・説明会などによるものを含みます。）、キャンペーン・懸賞企画、アンケート実施のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（8）当社サービスの改善や新サービスの開発等に役⽴てるため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（9）マーケティングデータの調査・分析のため（取得した閲覧履歴や購買履歴等の情報を分析して趣味・嗜好に応じた新商品やサービスに関する広告掲載のため）</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（10）当社又は第三者のウェブサイト、Webメディア、SNSサービス、プレスリリース配信サービス、書籍、雑誌その他メディア上にユーザーの属性にあった広告を表示、掲載又は配信するため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（11）ユーザーが当社サービスにアクセスして利⽤する⽅法の把握、利⽤状況を分析するため（取得した行動履歴等の情報を分析してサイト構成やサービス内容の改善を図るため）</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（12）登録ユーザーの反社会的勢力の該当性を判断するため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（13）その他、当社のサービスにおいて個別に定める⽬的のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">クッキー(Cookie)及びアクセス解析ツールについて</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">「外部送信情報の取扱いについて」をご参照ください。</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の第三者への提供</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、次の各号に掲げる場合を除くほか、あらかじめユーザーの同意を得ないで、個人情報を第三者に提供いたしません。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（1）法令に基づく場合</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（2）人の生命、身体又は財産の保護のために必要がある場合であって、ユーザーの同意を得ることが困難であるとき。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（3）公衆衛生の向上又は児童の健全な育成の推進のために特に必要がある場合であって、ユーザーの同意を得ることが困難であるとき。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（4）国の機関若しくは地方公共団体又はその委託を受けた者が法令の定める事務を遂行することに対して協力する必要がある場合であって、ユーザーの同意を得ることにより当該事務の遂行に支障を及ぼすおそれがあるとき。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">業務委託先に対する情報の提供</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、利用目的の達成に必要な範囲内において業務の全部又は一部を委託することに伴って、委託先業者にユーザーの個人情報を提供する場合があります。当該提供を行う場合、当社は、当該委託先業者に対し、当社が許諾した目的の範囲内においてのみ個人情報を利用する義務を課し、当社と同等又はそれ以上の基準に従ってユーザーの個人情報を管理する義務を課します。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の保持、保存期間</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、ユーザーの個人情報に関して、その利用目的の遂行に必要な期間、又は適用法令等が定める期間、ユーザーの個人情報を保持、保存します。利用目的の達成後、又は適用法令等が定める期間終了後、ユーザーの個人情報は削除します。ただし、当社の法的義務の順守及び権利利益の保護のため、ユーザーの情報を保持、保存し利用する場合があります。</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0"><b><strong class="font-bold" style="white-space: pre-wrap;">&nbsp;</strong></b></h2><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の開示等</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、ユーザー本人から、個人情報の開示を求められたときは、ユーザー本人であることを確認させていただいた上で、遅滞なく開示します。ただし、開示することにより次のいずれかに該当する場合は、その全部又は一部を開示しないこともあり、開示しない決定をした場合には、その旨を遅滞なく通知します。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（1）ご本人又は第三者の生命、身体、財産その他の権利利益を害するおそれがある場合</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（2）当社の業務の適正な実施に著しい支障を及ぼすおそれがある場合</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（3）他の法令に違反することとなる場合</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0"><b><strong class="font-bold" style="white-space: pre-wrap;">&nbsp;</strong></b></h2><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の管理</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、個人情報の漏えい、滅失又は毀損の防止その他の安全管理のために必要かつ適切な措置を講じます。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の訂正等</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、ユーザー本人より当該本人の個人情報の訂正、追加、削除、利用の停止又は消去を求められた場合には、ユーザー本人であることを確認させていただいた上で合理的な期間内に対応いたします。末尾に記載のお問い合わせ先よりご連絡ください。</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0"><b><strong class="font-bold" style="white-space: pre-wrap;">&nbsp;</strong></b></h2><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">プライバシーポリシーの更新について</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、個人情報保護を図るため、法令等の変更や必要に応じて、プライバシーポリシーを改訂することがあります。その際は、最新のプライバシーポリシーを本サービスのサイト内に掲載いたします。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">免責</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社及び本サービスでは、お客様のID及びパスワードの管理については関与いたしません。お客様の不注意によりID及びパスワードが第三者に利用された場合は、ご登録いただいている個人情報を閲覧される可能性がございますので、ご利用にあたっては、使用及び管理について責任を負うと共に、使用上の過誤又は第三者による不正使用等について十分ご注意ください。</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0"><b><strong class="font-bold" style="white-space: pre-wrap;">&nbsp;</strong></b></h2><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">お問い合わせ先</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><a href="mailto:個人情報に関するお問い合わせ先は、<EMAIL>" class="text-blue-600 hover:underline hover:cursor-pointer"><span style="white-space: pre-wrap;">個人情報に関するお問い合わせ先は、<EMAIL></span></a><span style="white-space: pre-wrap;"> までお願い致します。</span><br><span style="white-space: pre-wrap;">デジタルギア株式会社　V祭事業部</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><br><span style="white-space: pre-wrap;">2025年5月8日 制定</span></p>`,
			Language: "en-us",
		},
		{
			Key:      "privacy-policy",
			Value:    `<h1 class="scroll-m-20 text-4xl text-primary my-6 font-extrabold tracking-tight lg:text-5xl" dir="ltr" style="text-align: center;"><b><strong class="font-bold" style="white-space: pre-wrap;">プライバシーポリシー</strong></b></h1><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><br><span style="white-space: pre-wrap;">デジタルギア株式会社（以下「当社」といいます。）の運営する「V祭（ぶいさい）」（以下「本サービス」といいます。）は、以下のプライバシーポリシーに基づき、適正に個人情報を収集し取り扱います。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報収集について</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、ユーザーに関する次の各号に掲げる個人情報を取得します。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（1）本人に関する情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">氏名、住所、郵便番号、性別、生年月日、電話番号、メールアドレス、会員ID、パスワード、外部SNSサービスのアカウント情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（2）本サービスの利用に関する情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">本サービスの申込内容、購入履歴に関する情報等</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（3）決済に関する情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">金融機関口座に関する情報、決済及びその方法に関する情報等</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（4）ブラウザ上で取得する情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">Cookie等のオンライン上の識別子、端末情報、位置情報、閲覧履歴、IPアドレスの情報等</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（5）お問い合わせに関する情報</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">ユーザーから当社へのお問い合わせ・ご連絡に関する情報等</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0"><b><strong class="font-bold" style="white-space: pre-wrap;">&nbsp;</strong></b></h2><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の利用について</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、本サービスを通して収集したユーザーの個⼈情報を当社の事業運営のために次の各号に定める⽬的で利⽤することができるものとします。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（1）本人確認、本人認証のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（2）アフターサービス、お問い合わせ、苦情対応のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（3）当社及び本サービスへのお問い合わせ、本サービスの運営上必要な事項の通知（電⼦メールによるものを含むものとします。）のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（4）ユーザーが投稿した情報の掲載のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（5）システムの維持、不具合対応のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（6）メールマガジン、ダイレクトメール等の広告物送信のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（7）当社のサービス・商品等に関するご案内（セミナー・説明会などによるものを含みます。）、キャンペーン・懸賞企画、アンケート実施のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（8）当社サービスの改善や新サービスの開発等に役⽴てるため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（9）マーケティングデータの調査・分析のため（取得した閲覧履歴や購買履歴等の情報を分析して趣味・嗜好に応じた新商品やサービスに関する広告掲載のため）</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（10）当社又は第三者のウェブサイト、Webメディア、SNSサービス、プレスリリース配信サービス、書籍、雑誌その他メディア上にユーザーの属性にあった広告を表示、掲載又は配信するため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（11）ユーザーが当社サービスにアクセスして利⽤する⽅法の把握、利⽤状況を分析するため（取得した行動履歴等の情報を分析してサイト構成やサービス内容の改善を図るため）</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（12）登録ユーザーの反社会的勢力の該当性を判断するため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（13）その他、当社のサービスにおいて個別に定める⽬的のため</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">クッキー(Cookie)及びアクセス解析ツールについて</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">「外部送信情報の取扱いについて」をご参照ください。</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の第三者への提供</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、次の各号に掲げる場合を除くほか、あらかじめユーザーの同意を得ないで、個人情報を第三者に提供いたしません。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（1）法令に基づく場合</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（2）人の生命、身体又は財産の保護のために必要がある場合であって、ユーザーの同意を得ることが困難であるとき。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（3）公衆衛生の向上又は児童の健全な育成の推進のために特に必要がある場合であって、ユーザーの同意を得ることが困難であるとき。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（4）国の機関若しくは地方公共団体又はその委託を受けた者が法令の定める事務を遂行することに対して協力する必要がある場合であって、ユーザーの同意を得ることにより当該事務の遂行に支障を及ぼすおそれがあるとき。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">業務委託先に対する情報の提供</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、利用目的の達成に必要な範囲内において業務の全部又は一部を委託することに伴って、委託先業者にユーザーの個人情報を提供する場合があります。当該提供を行う場合、当社は、当該委託先業者に対し、当社が許諾した目的の範囲内においてのみ個人情報を利用する義務を課し、当社と同等又はそれ以上の基準に従ってユーザーの個人情報を管理する義務を課します。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の保持、保存期間</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、ユーザーの個人情報に関して、その利用目的の遂行に必要な期間、又は適用法令等が定める期間、ユーザーの個人情報を保持、保存します。利用目的の達成後、又は適用法令等が定める期間終了後、ユーザーの個人情報は削除します。ただし、当社の法的義務の順守及び権利利益の保護のため、ユーザーの情報を保持、保存し利用する場合があります。</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0"><b><strong class="font-bold" style="white-space: pre-wrap;">&nbsp;</strong></b></h2><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の開示等</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、ユーザー本人から、個人情報の開示を求められたときは、ユーザー本人であることを確認させていただいた上で、遅滞なく開示します。ただし、開示することにより次のいずれかに該当する場合は、その全部又は一部を開示しないこともあり、開示しない決定をした場合には、その旨を遅滞なく通知します。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（1）ご本人又は第三者の生命、身体、財産その他の権利利益を害するおそれがある場合</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（2）当社の業務の適正な実施に著しい支障を及ぼすおそれがある場合</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">（3）他の法令に違反することとなる場合</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0"><b><strong class="font-bold" style="white-space: pre-wrap;">&nbsp;</strong></b></h2><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の管理</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、個人情報の漏えい、滅失又は毀損の防止その他の安全管理のために必要かつ適切な措置を講じます。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">個人情報の訂正等</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、ユーザー本人より当該本人の個人情報の訂正、追加、削除、利用の停止又は消去を求められた場合には、ユーザー本人であることを確認させていただいた上で合理的な期間内に対応いたします。末尾に記載のお問い合わせ先よりご連絡ください。</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0"><b><strong class="font-bold" style="white-space: pre-wrap;">&nbsp;</strong></b></h2><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">プライバシーポリシーの更新について</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社は、個人情報保護を図るため、法令等の変更や必要に応じて、プライバシーポリシーを改訂することがあります。その際は、最新のプライバシーポリシーを本サービスのサイト内に掲載いたします。</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6"><span style="white-space: pre-wrap;">&nbsp;</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">免責</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><span style="white-space: pre-wrap;">当社及び本サービスでは、お客様のID及びパスワードの管理については関与いたしません。お客様の不注意によりID及びパスワードが第三者に利用された場合は、ご登録いただいている個人情報を閲覧される可能性がございますので、ご利用にあたっては、使用及び管理について責任を負うと共に、使用上の過誤又は第三者による不正使用等について十分ご注意ください。</span></p><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0"><b><strong class="font-bold" style="white-space: pre-wrap;">&nbsp;</strong></b></h2><h2 class="scroll-m-20 border-b text-primary my-6 text-3xl font-semibold tracking-tight first:mt-0" dir="ltr"><b><strong class="font-bold" style="white-space: pre-wrap;">お問い合わせ先</strong></b></h2><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><a href="mailto:個人情報に関するお問い合わせ先は、<EMAIL>" class="text-blue-600 hover:underline hover:cursor-pointer"><span style="white-space: pre-wrap;">個人情報に関するお問い合わせ先は、<EMAIL></span></a><span style="white-space: pre-wrap;"> までお願い致します。</span><br><span style="white-space: pre-wrap;">デジタルギア株式会社　V祭事業部</span></p><p class="leading-7 [&amp;:not(:first-child)]:mt-6" dir="ltr"><br><span style="white-space: pre-wrap;">2025年5月8日 制定</span></p>`,
			Language: "ja-jp",
		},

		{
			Key:      "terms-condition",
			Value:    `<p class="leading-7 [&amp;:not(:first-child)]:mt-6"><br></p>`,
			Language: "en-us",
		},
		{
			Key:      "terms-condition",
			Value:    `<p class="leading-7 [&amp;:not(:first-child)]:mt-6"><br></p>`,
			Language: "ja-jp",
		},

		{
			Key:      "community-guidelines",
			Value:    `<p class="leading-7 [&amp;:not(:first-child)]:mt-6"><br></p>`,
			Language: "en-us",
		},
		{
			Key:      "community-guidelines",
			Value:    `<p class="leading-7 [&amp;:not(:first-child)]:mt-6"><br></p>`,
			Language: "ja-jp",
		},

		{
			Key:      "operating-company",
			Value:    `<p class="leading-7 [&amp;:not(:first-child)]:mt-6"><br></p>`,
			Language: "en-us",
		},
		{
			Key:      "operating-company",
			Value:    `<p class="leading-7 [&amp;:not(:first-child)]:mt-6"><br></p>`,
			Language: "ja-jp",
		},
		{
			Key:      "crowdfunding-guidelines",
			Value:    `<p class="leading-7 [&amp;:not(:first-child)]:mt-6"><br></p>`,
			Language: "en-us",
		},
		{
			Key:      "crowdfunding-guidelines",
			Value:    `<p class="leading-7 [&amp;:not(:first-child)]:mt-6"><br></p>`,
			Language: "ja-jp",
		},
		{
			Key:      "transaction-act",
			Value:    `<p class="leading-7 [&amp;:not(:first-child)]:mt-6"><br></p>`,
			Language: "en-us",
		},
		{
			Key:      "transaction-act",
			Value:    `<p class="leading-7 [&amp;:not(:first-child)]:mt-6"><br></p>`,
			Language: "ja-jp",
		},
	}

	db.Create(&staticData)

}

func getCampaignBanners() []model.CampaignBanner {
	banners := generateSlice(func(i int) (model.CampaignBanner, error) {
		return model.CampaignBanner{
			Image: getRandomImage(),
			Index: int32(i + 1),
		}, nil
	}, faker.Number(4, 10))
	return banners
}

func getCampaignVariants() []model.CampaignVariant {
	variants := generateSlice(func(i int) (model.CampaignVariant, error) {
		return model.CampaignVariant{
			Description:  getVeryShortParagraph(),
			Price:        int32(faker.Number(1000, 10000)),
			MaxSub:       int32(faker.Number(0, 10)),
			Image:        getRandomImage(),
			Title:        faker.Username(),
			DisplayOrder: int32(i + 1),
		}, nil
	}, faker.Number(4, 10))
	return variants
}
func seedUsers() {
	password, err := hasher.Hash("password")
	if err != nil {
		panic(err)
	}
	passwords := users.LastThreePasswords{}
	passwords.AddPassword(password)

	maxDob := time.Now().Add(-24 * 365 * 18 * time.Hour) // 18 years ago
	minDob := time.Now().Add(-24 * 365 * 30 * time.Hour) // 30 years ago

	twitter := "https://twitter.com/username"
	twitch := "https://twitch.tv/username"
	yt := "https://youtube.com/username"
	tiktok := "https://tiktok.com/username"
	instagram := "https://instagram.com/username"
	Discord := "https://discord.com/username"

	socialmediaLinks := sharedv1.SocialMediaLinks{
		Twitter:   &twitter,
		Youtube:   &yt,
		Twitch:    &twitch,
		Tiktok:    &tiktok,
		Instagram: &instagram,
		Discord:   &Discord,
	}

	users := generateSlice(func(i int) (model.User, error) {
		person := faker.Person()
		return model.User{
			FullName:      person.FirstName + " " + person.LastName,
			Email:         fmt.Sprintf("<EMAIL>", i+1),
			EmailVerified: true,
			Image:         getRandomImage(),
			DateOfBirth:   faker.DateRange(minDob, maxDob),
			Role:          "user",
			Accounts: []model.Account{
				{
					ProviderID:        "email",
					Password:          password,
					LastThreePassword: string(passwords.ToByte()),
				},
			},
		}, nil
	}, 200)

	db.Create(&users)

	for _, user := range users {
		date := time.Now().Add(24 * time.Hour)
		address := model.UserDeliveryAddress{
			UserID:                user.ID,
			Recipient:             faker.Username(),
			PhoneNumber:           faker.Phone(),
			PostalCode:            strconv.Itoa(faker.Number(1000, 5000)),
			Prefecture:            "Hokkaido/北海道",
			City:                  faker.City(),
			AddressLine1:          faker.Street(),
			AddressLine2:          faker.Street(),
			PreferredDeliveryTime: "2PM~4PM（午後2時～4時",
			PreferredDeliveryDate: &date,
		}

		err := db.Save(&address).Error

		if err != nil {
			panic(err)
		}
	}

	for i := range 40 {

		campaginName := faker.ProductName()
		cat := []model.Category{getRandomCategory()}

		campaigns := generateSlice(func(i int) (model.Campaign, error) {
			return model.Campaign{
				Name:               campaginName,
				Description:        getLongParagraph(),
				ShortDescription:   faker.Paragraph(1, 2, 5, "\n"),
				Category:           cat,
				Thumbnail:          getRandomImage(),
				StartDate:          faker.DateRange(time.Now(), time.Now().Add(30*24*time.Hour)),
				EndDate:            faker.DateRange(time.Now().Add(30*24*time.Hour), time.Now().Add(60*24*time.Hour)),
				TotalBudget:        int32(faker.Number(10000, 1000000)),
				PromotionalMessage: faker.Paragraph(2, 2, 8, "\n"),
				SocialMediaLinks:   string(socialmediaLinks.ToBytes()),
				CampaignBanners:    getCampaignBanners(),
				CampaignVariants:   getCampaignVariants(),
				Slug:               helpers.SlugifyWithTimestamp(campaginName),
			}, nil
		}, faker.Number(2, 8))

		postTitle := faker.BookTitle()

		posts := generateSlice(func(i int) (model.Post, error) {
			var campaign *int64

			if i%2 == 0 {
				campaign = &getRandomCampaigns(campaigns).ID
			}

			return model.Post{
				Title:            postTitle,
				Name:             faker.BookTitle(),
				MembershipOnly:   faker.Bool(),
				Media:            getRandomImage(),
				MediaType:        "picture",
				ShortDescription: faker.Sentence(10),
				Description:      getShortParagraph(),
				CategoryID:       getRandomCategory().ID,
				Slug:             helpers.SlugifyWithTimestamp(postTitle),
				CampaignID:       campaign,
				PostComments: helpers.Map(getRandomNElements(users, faker.Number(0, 20)), func(user model.User) model.PostComment {
					return model.PostComment{
						UserID:    user.ID,
						Content:   faker.Paragraph(1, 1, 10, "\n"),
						CreatedAt: time.Now().Add(-time.Duration(faker.Number(1, 100)) * time.Hour),
					}
				}),
				PostLikes: helpers.Map(getRandomNElements(users, faker.Number(0, 180)), func(user model.User) model.PostLike {
					return model.PostLike{
						UserID:    user.ID,
						CreatedAt: time.Now().Add(-time.Duration(faker.Number(1, 100)) * time.Hour),
					}
				}),
			}, nil
		}, faker.Number(10, 20))

		person := faker.Person()
		vtuber := model.User{
			FullName:      person.FirstName + " " + person.LastName,
			Email:         fmt.Sprintf("<EMAIL>", i+1),
			EmailVerified: true,
			DateOfBirth:   faker.DateRange(minDob, maxDob),
			Image:         getRandomImage(),
			Role:          "vtuber",
			Accounts: []model.Account{
				{
					ProviderID:        "email",
					Password:          password,
					LastThreePassword: string(passwords.ToByte()),
				},
			},
			VtuberProfile: model.VtuberProfile{
				Username:         strings.ToLower(faker.Username()) + strconv.Itoa(i+1),
				DisplayName:      faker.Username(),
				Furigana:         faker.Username(),
				SocialMediaLinks: string(socialmediaLinks.ToBytes()),
				Description:      faker.Paragraph(1, 5, 10, "\n"),
				Image:            getRandomImage(),
				BannerImage:      getRandomImage(),
				VtuberPlans: generateSlice(func(i int) (model.VtuberPlan, error) {
					return model.VtuberPlan{
						Description:      getShortParagraphList(),
						Title:            faker.Username(),
						ShortDescription: faker.Paragraph(1, 1, 10, "\n"),
						Price:            int32((i + 1) * 300),
						Index:            int32(i),
						AnnualPrice:      int32((i + 1) * 2900),
					}, nil
				}, 3),
				Categories: cat,
				Campaigns:  campaigns,
				Posts:      posts,
				VtuberBanners: generateSlice(func(i int) (model.VtuberBanner, error) {
					return model.VtuberBanner{
						Image: getRandomImage(),
					}, nil
				}, faker.Number(4, 10)),
				VtuberGalleries: generateSlice(func(i int) (model.VtuberGallery, error) {
					return model.VtuberGallery{
						Media:       getRandomImage(),
						MediaType:   "picture",
						Description: faker.Paragraph(1, 1, 10, "\n"),
					}, nil
				}, 50),
			},
		}

		err := db.Create(&vtuber).Error
		if err != nil {
			panic(err)
		}

		vtubers = append(vtubers, vtuber)

		for _, campaign := range vtuber.VtuberProfile.Campaigns {
			for _, variant := range campaign.CampaignVariants {

				maxSub := int(variant.MaxSub)

				if maxSub == 0 {
					maxSub = faker.Number(10, 30)
				}

				variant.CampaignVariantSubscriptions = helpers.Map(getRandomNElements(users, faker.Number(0, maxSub)), func(user model.User) model.CampaignVariantSubscription {
					return model.CampaignVariantSubscription{
						UserID:            user.ID,
						CampaignVariantID: variant.ID,
						VtuberID:          vtuber.VtuberProfile.ID,
						Price:             variant.Price,
						Comment:           faker.Paragraph(1, 1, 10, "\n"),
						CampaignID:        campaign.ID,
						CreatedAt:         time.Now().Add(-time.Duration(faker.Number(1, 100)) * time.Hour),
					}
				})

				err := db.Save(&variant).Error

				if err != nil {
					panic(err)
				}

			}
		}

		for _, plan := range vtuber.VtuberProfile.VtuberPlans {

			subs := helpers.Map(getRandomNElements(users, faker.Number(2, 10)), func(user model.User) model.VtuberUserSubscription {
				return model.VtuberUserSubscription{
					UserID:       user.ID,
					VtuberID:     vtuber.VtuberProfile.ID,
					VtuberPlanID: plan.ID,
					CreatedAt:    time.Now().Add(-time.Duration(faker.Number(1, 100)) * time.Hour),
					IsRecurring:  faker.Bool(),
					ExpiresOn:    time.Now().Add(time.Duration(faker.Number(1, 100)) * time.Hour),
				}
			})

			err := db.Create(&subs).Error
			if err != nil {
				panic(err)
			}

		}

	}

	radomEventCreators := getRandomNElements(vtubers, faker.Number(5, 10))
	randomUsers := getRandomNElements(users, faker.Number(5, 10))

	radomEventCreators = append(radomEventCreators, randomUsers...)

	for _, user := range radomEventCreators {
		cat := []model.Category{getRandomCategory()}

		events := generateSlice(func(i int) (model.Event, error) {
			status := faker.RandomString([]string{"pending", "approved"})
			var vtuberId *int64
			if user.Role == "vtuber" {
				vtuberId = &user.VtuberProfile.ID
			}
			eventComments := make([]model.EventComment, 0)
			eventParticipants := make([]model.EventParticipant, 0)

			if status == "approved" {
				eventComments = helpers.Map(getRandomNElements(users, faker.Number(0, 20)), func(user model.User) model.EventComment {
					return model.EventComment{
						UserID:    user.ID,
						Content:   faker.Paragraph(1, 1, 10, "\n"),
						CreatedAt: time.Now().Add(-time.Duration(faker.Number(1, 100)) * time.Hour),
					}
				})
				eventParticipants = helpers.Map(getRandomNElements(vtubers, faker.Number(20, 30)), func(user model.User) model.EventParticipant {
					random := faker.RandomUint([]uint{0, 1, 2, 3, 4})
					participantStatus := "pending"

					if random > 0 {
						participantStatus = "accepted"
					}

					return model.EventParticipant{
						VtuberID: user.VtuberProfile.ID,
						Status:   participantStatus,
					}
				})
			}

			title := faker.BookTitle()

			return model.Event{
				Title:             title,
				Description:       "",
				Image:             getRandomImage(),
				StartDate:         faker.DateRange(time.Now(), time.Now().Add(-20*24*time.Hour)),
				EndDate:           faker.DateRange(time.Now().Add(30*24*time.Hour), time.Now().Add(60*24*time.Hour)),
				UserID:            user.ID,
				SocialMediaLinks:  string(socialmediaLinks.ToBytes()),
				Category:          cat,
				ShortDescription:  faker.Paragraph(1, 2, 5, "\n"),
				Rules:             getLongParagraph(),
				Requirements:      getLongParagraph(),
				Benefits:          getLongParagraph(),
				Overview:          getLongParagraph(),
				ParticipationFlow: getLongParagraph(),
				Status:            status,
				VtuberProfileID:   vtuberId,
				EventComments:     eventComments,
				EventParticipants: eventParticipants,
				Slug:              helpers.SlugifyWithTimestamp(title),
			}, nil
		}, faker.Number(1, 3))

		err := db.Create(&events).Error

		if err != nil {
			panic(err)
		}

		for _, event := range events {
			for _, participant := range event.EventParticipants {
				if participant.Status == "accepted" {
					votes := generateSlice(func(i int) (model.EventParticipantVote, error) {
						return model.EventParticipantVote{
							UserID:             getRandomElement(users).ID,
							EventParticipantID: participant.ID,
							Count:              int32(faker.Number(10, 100)),
							Type:               "user_point",
							EventID:            event.ID,
							CreatedAt:          time.Now().Add(-time.Duration(faker.Number(1, 100)) * time.Hour),
						}, nil
					}, faker.Number(20, 100))
					err := db.Create(&votes).Error
					if err != nil {
						panic(err)
					}

					db.Create(votes)
				}
			}
		}

	}

}

func seedFaqs() {
	faqsJA := generateSlice(func(i int) (model.Faq, error) {
		return model.Faq{
			Question: faker.Question(),
			Response: faker.Sentence(20),
			Index:    int32(i + 1),
			Active:   true,
			Language: "ja-jp",
			Tag:      getRandomElement(helpers.TagsJa),
		}, nil
	}, 10)
	faqsEn := generateSlice(func(i int) (model.Faq, error) {
		return model.Faq{
			Question: faker.Question(),
			Response: faker.Sentence(20),
			Index:    int32(i + 1),
			Active:   true,
			Language: "en-us",
			Tag:      getRandomElement(helpers.TagsEng),
		}, nil
	}, 10)

	db.Create(&faqsJA)
	db.Create(&faqsEn)
}
