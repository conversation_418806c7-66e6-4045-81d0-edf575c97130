// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file billing/v1/user_billing_infos.proto (package api.billing.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file billing/v1/user_billing_infos.proto.
 */
export const file_billing_v1_user_billing_infos: GenFile = /*@__PURE__*/
  fileDesc("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", [file_authz_v1_authz]);

/**
 * @generated from message api.billing.v1.AddBillingInfoRequest
 */
export type AddBillingInfoRequest = Message<"api.billing.v1.AddBillingInfoRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string full_name = 1;
   */
  fullName: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string address_1 = 2;
   */
  address1: string;

  /**
   * @generated from field: optional string address_2 = 3;
   */
  address2?: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string city = 4;
   */
  city: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string state = 5;
   */
  state: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string country = 6;
   */
  country: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string postal_code = 7;
   */
  postalCode: string;

  /**
   * @generated from field: optional string company_name = 8;
   */
  companyName?: string;

  /**
   * @generated from field: optional string vat_number = 9;
   */
  vatNumber?: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string card_no = 10;
   */
  cardNo: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string card_expiry = 11;
   */
  cardExpiry: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string card_cvc = 12;
   */
  cardCvc: string;
};

/**
 * Describes the message api.billing.v1.AddBillingInfoRequest.
 * Use `create(AddBillingInfoRequestSchema)` to create a new message.
 */
export const AddBillingInfoRequestSchema: GenMessage<AddBillingInfoRequest> = /*@__PURE__*/
  messageDesc(file_billing_v1_user_billing_infos, 0);

/**
 * @generated from message api.billing.v1.AddBillingInfoResponse
 */
export type AddBillingInfoResponse = Message<"api.billing.v1.AddBillingInfoResponse"> & {
  /**
   * @generated from field: api.billing.v1.BillingInfo data = 1;
   */
  data?: BillingInfo;
};

/**
 * Describes the message api.billing.v1.AddBillingInfoResponse.
 * Use `create(AddBillingInfoResponseSchema)` to create a new message.
 */
export const AddBillingInfoResponseSchema: GenMessage<AddBillingInfoResponse> = /*@__PURE__*/
  messageDesc(file_billing_v1_user_billing_infos, 1);

/**
 * @generated from message api.billing.v1.BillingInfo
 */
export type BillingInfo = Message<"api.billing.v1.BillingInfo"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string full_name = 2;
   */
  fullName: string;

  /**
   * @generated from field: string address_1 = 3;
   */
  address1: string;

  /**
   * @generated from field: optional string address_2 = 4;
   */
  address2?: string;

  /**
   * @generated from field: string city = 5;
   */
  city: string;

  /**
   * @generated from field: optional string state = 6;
   */
  state?: string;

  /**
   * @generated from field: string country = 7;
   */
  country: string;

  /**
   * @generated from field: string postal_code = 8;
   */
  postalCode: string;

  /**
   * @generated from field: optional string company_name = 9;
   */
  companyName?: string;

  /**
   * @generated from field: optional string vat_number = 10;
   */
  vatNumber?: string;

  /**
   * @generated from field: string card_no = 11;
   */
  cardNo: string;

  /**
   * @generated from field: string card_expiry = 12;
   */
  cardExpiry: string;
};

/**
 * Describes the message api.billing.v1.BillingInfo.
 * Use `create(BillingInfoSchema)` to create a new message.
 */
export const BillingInfoSchema: GenMessage<BillingInfo> = /*@__PURE__*/
  messageDesc(file_billing_v1_user_billing_infos, 2);

/**
 * @generated from message api.billing.v1.GetBillingInfoRequest
 */
export type GetBillingInfoRequest = Message<"api.billing.v1.GetBillingInfoRequest"> & {
};

/**
 * Describes the message api.billing.v1.GetBillingInfoRequest.
 * Use `create(GetBillingInfoRequestSchema)` to create a new message.
 */
export const GetBillingInfoRequestSchema: GenMessage<GetBillingInfoRequest> = /*@__PURE__*/
  messageDesc(file_billing_v1_user_billing_infos, 3);

/**
 * @generated from message api.billing.v1.GetBillingInfoResponse
 */
export type GetBillingInfoResponse = Message<"api.billing.v1.GetBillingInfoResponse"> & {
  /**
   * @generated from field: repeated api.billing.v1.BillingInfo data = 1;
   */
  data: BillingInfo[];
};

/**
 * Describes the message api.billing.v1.GetBillingInfoResponse.
 * Use `create(GetBillingInfoResponseSchema)` to create a new message.
 */
export const GetBillingInfoResponseSchema: GenMessage<GetBillingInfoResponse> = /*@__PURE__*/
  messageDesc(file_billing_v1_user_billing_infos, 4);

/**
 * @generated from message api.billing.v1.UpdateBillingInfoRequest
 */
export type UpdateBillingInfoRequest = Message<"api.billing.v1.UpdateBillingInfoRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string full_name = 2;
   */
  fullName: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string address_1 = 3;
   */
  address1: string;

  /**
   * @generated from field: optional string address_2 = 4;
   */
  address2?: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string city = 5;
   */
  city: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string state = 6;
   */
  state: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string country = 7;
   */
  country: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string postal_code = 8;
   */
  postalCode: string;

  /**
   * @generated from field: optional string company_name = 9;
   */
  companyName?: string;

  /**
   * @generated from field: optional string vat_number = 10;
   */
  vatNumber?: string;
};

/**
 * Describes the message api.billing.v1.UpdateBillingInfoRequest.
 * Use `create(UpdateBillingInfoRequestSchema)` to create a new message.
 */
export const UpdateBillingInfoRequestSchema: GenMessage<UpdateBillingInfoRequest> = /*@__PURE__*/
  messageDesc(file_billing_v1_user_billing_infos, 5);

/**
 * @generated from message api.billing.v1.UpdateBillingInfoResponse
 */
export type UpdateBillingInfoResponse = Message<"api.billing.v1.UpdateBillingInfoResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.billing.v1.UpdateBillingInfoResponse.
 * Use `create(UpdateBillingInfoResponseSchema)` to create a new message.
 */
export const UpdateBillingInfoResponseSchema: GenMessage<UpdateBillingInfoResponse> = /*@__PURE__*/
  messageDesc(file_billing_v1_user_billing_infos, 6);

/**
 * @generated from message api.billing.v1.DeleteBillingInfoResponse
 */
export type DeleteBillingInfoResponse = Message<"api.billing.v1.DeleteBillingInfoResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.billing.v1.DeleteBillingInfoResponse.
 * Use `create(DeleteBillingInfoResponseSchema)` to create a new message.
 */
export const DeleteBillingInfoResponseSchema: GenMessage<DeleteBillingInfoResponse> = /*@__PURE__*/
  messageDesc(file_billing_v1_user_billing_infos, 7);

/**
 * @generated from message api.billing.v1.DeleteBillingInfoRequest
 */
export type DeleteBillingInfoRequest = Message<"api.billing.v1.DeleteBillingInfoRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.billing.v1.DeleteBillingInfoRequest.
 * Use `create(DeleteBillingInfoRequestSchema)` to create a new message.
 */
export const DeleteBillingInfoRequestSchema: GenMessage<DeleteBillingInfoRequest> = /*@__PURE__*/
  messageDesc(file_billing_v1_user_billing_infos, 8);

/**
 * @generated from service api.billing.v1.BillInfoService
 */
export const BillInfoService: GenService<{
  /**
   * @generated from rpc api.billing.v1.BillInfoService.AddBillingInfo
   */
  addBillingInfo: {
    methodKind: "unary";
    input: typeof AddBillingInfoRequestSchema;
    output: typeof AddBillingInfoResponseSchema;
  },
  /**
   * @generated from rpc api.billing.v1.BillInfoService.GetBillingInfo
   */
  getBillingInfo: {
    methodKind: "unary";
    input: typeof GetBillingInfoRequestSchema;
    output: typeof GetBillingInfoResponseSchema;
  },
  /**
   * @generated from rpc api.billing.v1.BillInfoService.UpdateBillingInfo
   */
  updateBillingInfo: {
    methodKind: "unary";
    input: typeof UpdateBillingInfoRequestSchema;
    output: typeof UpdateBillingInfoResponseSchema;
  },
  /**
   * @generated from rpc api.billing.v1.BillInfoService.DeleteBillingInfo
   */
  deleteBillingInfo: {
    methodKind: "unary";
    input: typeof DeleteBillingInfoRequestSchema;
    output: typeof DeleteBillingInfoResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_billing_v1_user_billing_infos, 0);

