// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file notifications/v1/notifications.proto (package api.notifications.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type { PaginationDetails, PaginationRequest } from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file notifications/v1/notifications.proto.
 */
export const file_notifications_v1_notifications: GenFile = /*@__PURE__*/
  fileDesc("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", [file_authz_v1_authz, file_google_protobuf_timestamp, file_shared_v1_generic, file_shared_v1_pagination, file_shared_v1_profile, file_shared_v1_social_media_links]);

/**
 * @generated from message api.notifications.v1.GetNotificationsRequest
 */
export type GetNotificationsRequest = Message<"api.notifications.v1.GetNotificationsRequest"> & {
  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
   */
  pagination?: PaginationRequest;
};

/**
 * Describes the message api.notifications.v1.GetNotificationsRequest.
 * Use `create(GetNotificationsRequestSchema)` to create a new message.
 */
export const GetNotificationsRequestSchema: GenMessage<GetNotificationsRequest> = /*@__PURE__*/
  messageDesc(file_notifications_v1_notifications, 0);

/**
 * @generated from message api.notifications.v1.GetUserNotificationsResponse
 */
export type GetUserNotificationsResponse = Message<"api.notifications.v1.GetUserNotificationsResponse"> & {
  /**
   * @generated from field: repeated api.notifications.v1.Notification data = 1;
   */
  data: Notification[];

  /**
   * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
   */
  paginationDetails?: PaginationDetails;
};

/**
 * Describes the message api.notifications.v1.GetUserNotificationsResponse.
 * Use `create(GetUserNotificationsResponseSchema)` to create a new message.
 */
export const GetUserNotificationsResponseSchema: GenMessage<GetUserNotificationsResponse> = /*@__PURE__*/
  messageDesc(file_notifications_v1_notifications, 1);

/**
 * @generated from message api.notifications.v1.Notification
 */
export type Notification = Message<"api.notifications.v1.Notification"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: optional string json = 4;
   */
  json?: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: optional string deep_link = 6;
   */
  deepLink?: string;

  /**
   * @generated from field: string severity = 7;
   */
  severity: string;

  /**
   * @generated from field: string titleJp = 8;
   */
  titleJp: string;

  /**
   * @generated from field: string descriptionJp = 9;
   */
  descriptionJp: string;

  /**
   * @generated from field: bool is_read = 10;
   */
  isRead: boolean;
};

/**
 * Describes the message api.notifications.v1.Notification.
 * Use `create(NotificationSchema)` to create a new message.
 */
export const NotificationSchema: GenMessage<Notification> = /*@__PURE__*/
  messageDesc(file_notifications_v1_notifications, 2);

/**
 * @generated from message api.notifications.v1.MarkNotificationAsReadRequest
 */
export type MarkNotificationAsReadRequest = Message<"api.notifications.v1.MarkNotificationAsReadRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.notifications.v1.MarkNotificationAsReadRequest.
 * Use `create(MarkNotificationAsReadRequestSchema)` to create a new message.
 */
export const MarkNotificationAsReadRequestSchema: GenMessage<MarkNotificationAsReadRequest> = /*@__PURE__*/
  messageDesc(file_notifications_v1_notifications, 3);

/**
 * @generated from message api.notifications.v1.NotificationCountResponse
 */
export type NotificationCountResponse = Message<"api.notifications.v1.NotificationCountResponse"> & {
  /**
   * @generated from field: int64 count = 1;
   */
  count: bigint;
};

/**
 * Describes the message api.notifications.v1.NotificationCountResponse.
 * Use `create(NotificationCountResponseSchema)` to create a new message.
 */
export const NotificationCountResponseSchema: GenMessage<NotificationCountResponse> = /*@__PURE__*/
  messageDesc(file_notifications_v1_notifications, 4);

/**
 * @generated from message api.notifications.v1.NotificationCountRequest
 */
export type NotificationCountRequest = Message<"api.notifications.v1.NotificationCountRequest"> & {
};

/**
 * Describes the message api.notifications.v1.NotificationCountRequest.
 * Use `create(NotificationCountRequestSchema)` to create a new message.
 */
export const NotificationCountRequestSchema: GenMessage<NotificationCountRequest> = /*@__PURE__*/
  messageDesc(file_notifications_v1_notifications, 5);

/**
 * @generated from message api.notifications.v1.MarkNotificationAsReadResponse
 */
export type MarkNotificationAsReadResponse = Message<"api.notifications.v1.MarkNotificationAsReadResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.notifications.v1.MarkNotificationAsReadResponse.
 * Use `create(MarkNotificationAsReadResponseSchema)` to create a new message.
 */
export const MarkNotificationAsReadResponseSchema: GenMessage<MarkNotificationAsReadResponse> = /*@__PURE__*/
  messageDesc(file_notifications_v1_notifications, 6);

/**
 * @generated from message api.notifications.v1.MarkNotificationAsUnReadRequest
 */
export type MarkNotificationAsUnReadRequest = Message<"api.notifications.v1.MarkNotificationAsUnReadRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.notifications.v1.MarkNotificationAsUnReadRequest.
 * Use `create(MarkNotificationAsUnReadRequestSchema)` to create a new message.
 */
export const MarkNotificationAsUnReadRequestSchema: GenMessage<MarkNotificationAsUnReadRequest> = /*@__PURE__*/
  messageDesc(file_notifications_v1_notifications, 7);

/**
 * @generated from message api.notifications.v1.MarkNotificationAsUnReadResponse
 */
export type MarkNotificationAsUnReadResponse = Message<"api.notifications.v1.MarkNotificationAsUnReadResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.notifications.v1.MarkNotificationAsUnReadResponse.
 * Use `create(MarkNotificationAsUnReadResponseSchema)` to create a new message.
 */
export const MarkNotificationAsUnReadResponseSchema: GenMessage<MarkNotificationAsUnReadResponse> = /*@__PURE__*/
  messageDesc(file_notifications_v1_notifications, 8);

/**
 * @generated from message api.notifications.v1.DeleteNotificationByIdRequest
 */
export type DeleteNotificationByIdRequest = Message<"api.notifications.v1.DeleteNotificationByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.notifications.v1.DeleteNotificationByIdRequest.
 * Use `create(DeleteNotificationByIdRequestSchema)` to create a new message.
 */
export const DeleteNotificationByIdRequestSchema: GenMessage<DeleteNotificationByIdRequest> = /*@__PURE__*/
  messageDesc(file_notifications_v1_notifications, 9);

/**
 * @generated from message api.notifications.v1.DeleteNotificationByIdResponse
 */
export type DeleteNotificationByIdResponse = Message<"api.notifications.v1.DeleteNotificationByIdResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.notifications.v1.DeleteNotificationByIdResponse.
 * Use `create(DeleteNotificationByIdResponseSchema)` to create a new message.
 */
export const DeleteNotificationByIdResponseSchema: GenMessage<DeleteNotificationByIdResponse> = /*@__PURE__*/
  messageDesc(file_notifications_v1_notifications, 10);

/**
 * @generated from service api.notifications.v1.NotificationsService
 */
export const NotificationsService: GenService<{
  /**
   * @generated from rpc api.notifications.v1.NotificationsService.GetUserNotifications
   */
  getUserNotifications: {
    methodKind: "unary";
    input: typeof GetNotificationsRequestSchema;
    output: typeof GetUserNotificationsResponseSchema;
  },
  /**
   * @generated from rpc api.notifications.v1.NotificationsService.GetCreatorNotifications
   */
  getCreatorNotifications: {
    methodKind: "unary";
    input: typeof GetNotificationsRequestSchema;
    output: typeof GetUserNotificationsResponseSchema;
  },
  /**
   * @generated from rpc api.notifications.v1.NotificationsService.MarkNotificationAsRead
   */
  markNotificationAsRead: {
    methodKind: "unary";
    input: typeof MarkNotificationAsReadRequestSchema;
    output: typeof MarkNotificationAsReadResponseSchema;
  },
  /**
   * @generated from rpc api.notifications.v1.NotificationsService.MarkNotificationAsUnRead
   */
  markNotificationAsUnRead: {
    methodKind: "unary";
    input: typeof MarkNotificationAsUnReadRequestSchema;
    output: typeof MarkNotificationAsUnReadResponseSchema;
  },
  /**
   * @generated from rpc api.notifications.v1.NotificationsService.DeleteNotificationById
   */
  deleteNotificationById: {
    methodKind: "unary";
    input: typeof DeleteNotificationByIdRequestSchema;
    output: typeof DeleteNotificationByIdResponseSchema;
  },
  /**
   * @generated from rpc api.notifications.v1.NotificationsService.GetNotificationCount
   */
  getNotificationCount: {
    methodKind: "unary";
    input: typeof NotificationCountRequestSchema;
    output: typeof NotificationCountResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_notifications_v1_notifications, 0);

