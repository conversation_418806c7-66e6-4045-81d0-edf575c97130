// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file users/v1/users.proto (package api.users.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { GenericResponseSchema } from "../../shared/v1/generic_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type { PaginationDetails, PaginationRequest } from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { VtuberProfile } from "../../vtubers/v1/vtuberprofiles_pb";
import { file_vtubers_v1_vtuberprofiles } from "../../vtubers/v1/vtuberprofiles_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file users/v1/users.proto.
 */
export const file_users_v1_users: GenFile = /*@__PURE__*/
  fileDesc("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", [file_authz_v1_authz, file_google_protobuf_timestamp, file_shared_v1_generic, file_shared_v1_pagination, file_shared_v1_profile, file_shared_v1_social_media_links, file_vtubers_v1_vtuberprofiles]);

/**
 * @generated from message api.users.v1.GetUserCampaignInvestmentRequest
 */
export type GetUserCampaignInvestmentRequest = Message<"api.users.v1.GetUserCampaignInvestmentRequest"> & {
  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
   */
  pagination?: PaginationRequest;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 user_id = 2;
   */
  userId: bigint;
};

/**
 * Describes the message api.users.v1.GetUserCampaignInvestmentRequest.
 * Use `create(GetUserCampaignInvestmentRequestSchema)` to create a new message.
 */
export const GetUserCampaignInvestmentRequestSchema: GenMessage<GetUserCampaignInvestmentRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 0);

/**
 * @generated from message api.users.v1.GetUserCampaignInvestmentResponse
 */
export type GetUserCampaignInvestmentResponse = Message<"api.users.v1.GetUserCampaignInvestmentResponse"> & {
  /**
   * @generated from field: repeated api.users.v1.CampaignInvestment data = 1;
   */
  data: CampaignInvestment[];

  /**
   * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
   */
  paginationDetails?: PaginationDetails;
};

/**
 * Describes the message api.users.v1.GetUserCampaignInvestmentResponse.
 * Use `create(GetUserCampaignInvestmentResponseSchema)` to create a new message.
 */
export const GetUserCampaignInvestmentResponseSchema: GenMessage<GetUserCampaignInvestmentResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 1);

/**
 * @generated from message api.users.v1.CampaignInvestment
 */
export type CampaignInvestment = Message<"api.users.v1.CampaignInvestment"> & {
  /**
   * @generated from field: int64 user_id = 2;
   */
  userId: bigint;

  /**
   * @generated from field: int64 campaign_id = 3;
   */
  campaignId: bigint;

  /**
   * @generated from field: int32 amount = 4;
   */
  amount: number;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: int64 campaign_variant_id = 6;
   */
  campaignVariantId: bigint;

  /**
   * @generated from field: string campaign_variant_title = 7;
   */
  campaignVariantTitle: string;

  /**
   * @generated from field: string campaign_variant_image = 8;
   */
  campaignVariantImage: string;

  /**
   * @generated from field: string campaign_variant_description = 9;
   */
  campaignVariantDescription: string;

  /**
   * @generated from field: string campaign_name = 10;
   */
  campaignName: string;

  /**
   * @generated from field: string campaign_thumbnail = 11;
   */
  campaignThumbnail: string;
};

/**
 * Describes the message api.users.v1.CampaignInvestment.
 * Use `create(CampaignInvestmentSchema)` to create a new message.
 */
export const CampaignInvestmentSchema: GenMessage<CampaignInvestment> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 2);

/**
 * @generated from message api.users.v1.GetUserPointRequest
 */
export type GetUserPointRequest = Message<"api.users.v1.GetUserPointRequest"> & {
};

/**
 * Describes the message api.users.v1.GetUserPointRequest.
 * Use `create(GetUserPointRequestSchema)` to create a new message.
 */
export const GetUserPointRequestSchema: GenMessage<GetUserPointRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 3);

/**
 * @generated from message api.users.v1.GetUserResponse
 */
export type GetUserResponse = Message<"api.users.v1.GetUserResponse"> & {
  /**
   * @generated from field: float point = 1;
   */
  point: number;
};

/**
 * Describes the message api.users.v1.GetUserResponse.
 * Use `create(GetUserResponseSchema)` to create a new message.
 */
export const GetUserResponseSchema: GenMessage<GetUserResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 4);

/**
 * @generated from message api.users.v1.User
 */
export type User = Message<"api.users.v1.User"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string full_name = 2;
   */
  fullName: string;

  /**
   * @generated from field: optional string email = 3;
   */
  email?: string;

  /**
   * @generated from field: optional string image = 4;
   */
  image?: string;

  /**
   * @generated from field: string role = 5;
   */
  role: string;

  /**
   * @generated from field: bool email_verified = 6;
   */
  emailVerified: boolean;

  /**
   * @generated from field: google.protobuf.Timestamp dob = 7;
   */
  dob?: Timestamp;

  /**
   * @generated from field: bool is_banned = 8;
   */
  isBanned: boolean;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 9;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: bool is_vtuber = 10;
   */
  isVtuber: boolean;

  /**
   * @generated from field: optional api.vtubers.v1.VtuberProfile vtuber = 11;
   */
  vtuber?: VtuberProfile;
};

/**
 * Describes the message api.users.v1.User.
 * Use `create(UserSchema)` to create a new message.
 */
export const UserSchema: GenMessage<User> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 5);

/**
 * @generated from message api.users.v1.UpdateUserResponse
 */
export type UpdateUserResponse = Message<"api.users.v1.UpdateUserResponse"> & {
  /**
   * @generated from field: api.users.v1.User data = 1;
   */
  data?: User;
};

/**
 * Describes the message api.users.v1.UpdateUserResponse.
 * Use `create(UpdateUserResponseSchema)` to create a new message.
 */
export const UpdateUserResponseSchema: GenMessage<UpdateUserResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 6);

/**
 * @generated from message api.users.v1.GetAllUsersRequest
 */
export type GetAllUsersRequest = Message<"api.users.v1.GetAllUsersRequest"> & {
  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
   */
  pagination?: PaginationRequest;

  /**
   * @generated from field: optional string email = 2;
   */
  email?: string;
};

/**
 * Describes the message api.users.v1.GetAllUsersRequest.
 * Use `create(GetAllUsersRequestSchema)` to create a new message.
 */
export const GetAllUsersRequestSchema: GenMessage<GetAllUsersRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 7);

/**
 * @generated from message api.users.v1.GetAllUsersResponse
 */
export type GetAllUsersResponse = Message<"api.users.v1.GetAllUsersResponse"> & {
  /**
   * @generated from field: repeated api.users.v1.User data = 1;
   */
  data: User[];

  /**
   * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
   */
  paginationDetails?: PaginationDetails;
};

/**
 * Describes the message api.users.v1.GetAllUsersResponse.
 * Use `create(GetAllUsersResponseSchema)` to create a new message.
 */
export const GetAllUsersResponseSchema: GenMessage<GetAllUsersResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 8);

/**
 * @generated from message api.users.v1.GetAllDeletedUsersRequest
 */
export type GetAllDeletedUsersRequest = Message<"api.users.v1.GetAllDeletedUsersRequest"> & {
  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
   */
  pagination?: PaginationRequest;

  /**
   * @generated from field: optional string email = 2;
   */
  email?: string;
};

/**
 * Describes the message api.users.v1.GetAllDeletedUsersRequest.
 * Use `create(GetAllDeletedUsersRequestSchema)` to create a new message.
 */
export const GetAllDeletedUsersRequestSchema: GenMessage<GetAllDeletedUsersRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 9);

/**
 * @generated from message api.users.v1.GetAllDeletedUsersResponse
 */
export type GetAllDeletedUsersResponse = Message<"api.users.v1.GetAllDeletedUsersResponse"> & {
  /**
   * @generated from field: repeated api.users.v1.User data = 1;
   */
  data: User[];

  /**
   * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
   */
  paginationDetails?: PaginationDetails;
};

/**
 * Describes the message api.users.v1.GetAllDeletedUsersResponse.
 * Use `create(GetAllDeletedUsersResponseSchema)` to create a new message.
 */
export const GetAllDeletedUsersResponseSchema: GenMessage<GetAllDeletedUsersResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 10);

/**
 * @generated from message api.users.v1.GetUserByIdRequest
 */
export type GetUserByIdRequest = Message<"api.users.v1.GetUserByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.users.v1.GetUserByIdRequest.
 * Use `create(GetUserByIdRequestSchema)` to create a new message.
 */
export const GetUserByIdRequestSchema: GenMessage<GetUserByIdRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 11);

/**
 * @generated from message api.users.v1.GetUserByIdResponse
 */
export type GetUserByIdResponse = Message<"api.users.v1.GetUserByIdResponse"> & {
  /**
   * @generated from field: api.users.v1.User data = 1;
   */
  data?: User;
};

/**
 * Describes the message api.users.v1.GetUserByIdResponse.
 * Use `create(GetUserByIdResponseSchema)` to create a new message.
 */
export const GetUserByIdResponseSchema: GenMessage<GetUserByIdResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 12);

/**
 * @generated from message api.users.v1.DeleteUserByIdRequest
 */
export type DeleteUserByIdRequest = Message<"api.users.v1.DeleteUserByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.users.v1.DeleteUserByIdRequest.
 * Use `create(DeleteUserByIdRequestSchema)` to create a new message.
 */
export const DeleteUserByIdRequestSchema: GenMessage<DeleteUserByIdRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 13);

/**
 * @generated from message api.users.v1.UpdateUserByIdRequest
 */
export type UpdateUserByIdRequest = Message<"api.users.v1.UpdateUserByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string full_name = 1;
   */
  fullName: string;

  /**
   * @generated from field: string image = 2;
   */
  image: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 3;
   */
  id: bigint;
};

/**
 * Describes the message api.users.v1.UpdateUserByIdRequest.
 * Use `create(UpdateUserByIdRequestSchema)` to create a new message.
 */
export const UpdateUserByIdRequestSchema: GenMessage<UpdateUserByIdRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 14);

/**
 * @generated from message api.users.v1.BanUserRequest
 */
export type BanUserRequest = Message<"api.users.v1.BanUserRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.users.v1.BanUserRequest.
 * Use `create(BanUserRequestSchema)` to create a new message.
 */
export const BanUserRequestSchema: GenMessage<BanUserRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_users, 15);

/**
 * @generated from service api.users.v1.UserService
 */
export const UserService: GenService<{
  /**
   *  rpc AddUser(AddUserRequest) returns (AddUserResponse);
   *
   * @generated from rpc api.users.v1.UserService.GetAllUsers
   */
  getAllUsers: {
    methodKind: "unary";
    input: typeof GetAllUsersRequestSchema;
    output: typeof GetAllUsersResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.UserService.GetAllDeletedUsers
   */
  getAllDeletedUsers: {
    methodKind: "unary";
    input: typeof GetAllDeletedUsersRequestSchema;
    output: typeof GetAllDeletedUsersResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.UserService.GetUserById
   */
  getUserById: {
    methodKind: "unary";
    input: typeof GetUserByIdRequestSchema;
    output: typeof GetUserByIdResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.UserService.DeleteUserById
   */
  deleteUserById: {
    methodKind: "unary";
    input: typeof DeleteUserByIdRequestSchema;
    output: typeof GenericResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.UserService.UpdateUserById
   */
  updateUserById: {
    methodKind: "unary";
    input: typeof UpdateUserByIdRequestSchema;
    output: typeof UpdateUserResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.UserService.BanUser
   */
  banUser: {
    methodKind: "unary";
    input: typeof BanUserRequestSchema;
    output: typeof GenericResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.UserService.GetAllBannedUsers
   */
  getAllBannedUsers: {
    methodKind: "unary";
    input: typeof GetAllUsersRequestSchema;
    output: typeof GetAllUsersResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.UserService.GetUserPoint
   */
  getUserPoint: {
    methodKind: "unary";
    input: typeof GetUserPointRequestSchema;
    output: typeof GetUserResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.UserService.GetUserCampaignInvestment
   */
  getUserCampaignInvestment: {
    methodKind: "unary";
    input: typeof GetUserCampaignInvestmentRequestSchema;
    output: typeof GetUserCampaignInvestmentResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_users_v1_users, 0);

