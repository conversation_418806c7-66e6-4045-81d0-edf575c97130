// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file users/v1/auth.proto (package api.users.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { User } from "./users_pb";
import { file_users_v1_users } from "./users_pb";
import type { VtuberProfile } from "../../vtubers/v1/vtuberprofiles_pb";
import { file_vtubers_v1_vtuberprofiles } from "../../vtubers/v1/vtuberprofiles_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file users/v1/auth.proto.
 */
export const file_users_v1_auth: GenFile = /*@__PURE__*/
  fileDesc("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", [file_authz_v1_authz, file_google_protobuf_timestamp, file_users_v1_users, file_vtubers_v1_vtuberprofiles]);

/**
 * @generated from message api.users.v1.SignInWithSocialRequest
 */
export type SignInWithSocialRequest = Message<"api.users.v1.SignInWithSocialRequest"> & {
  /**
   * @generated from field: string provider = 1;
   */
  provider: string;

  /**
   * @generated from field: string code = 2;
   */
  code: string;
};

/**
 * Describes the message api.users.v1.SignInWithSocialRequest.
 * Use `create(SignInWithSocialRequestSchema)` to create a new message.
 */
export const SignInWithSocialRequestSchema: GenMessage<SignInWithSocialRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 0);

/**
 * @generated from message api.users.v1.SignInWithSocialResponse
 */
export type SignInWithSocialResponse = Message<"api.users.v1.SignInWithSocialResponse"> & {
  /**
   * @generated from field: string access_token = 1;
   */
  accessToken: string;

  /**
   * @generated from field: string refresh_token = 2;
   */
  refreshToken: string;
};

/**
 * Describes the message api.users.v1.SignInWithSocialResponse.
 * Use `create(SignInWithSocialResponseSchema)` to create a new message.
 */
export const SignInWithSocialResponseSchema: GenMessage<SignInWithSocialResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 1);

/**
 * @generated from message api.users.v1.GetSessionRequest
 */
export type GetSessionRequest = Message<"api.users.v1.GetSessionRequest"> & {
};

/**
 * Describes the message api.users.v1.GetSessionRequest.
 * Use `create(GetSessionRequestSchema)` to create a new message.
 */
export const GetSessionRequestSchema: GenMessage<GetSessionRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 2);

/**
 * @generated from message api.users.v1.GetSessionResponse
 */
export type GetSessionResponse = Message<"api.users.v1.GetSessionResponse"> & {
  /**
   * @generated from field: api.users.v1.User user = 1;
   */
  user?: User;

  /**
   * @generated from field: api.vtubers.v1.VtuberProfile vtuber = 2;
   */
  vtuber?: VtuberProfile;
};

/**
 * Describes the message api.users.v1.GetSessionResponse.
 * Use `create(GetSessionResponseSchema)` to create a new message.
 */
export const GetSessionResponseSchema: GenMessage<GetSessionResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 3);

/**
 * @generated from message api.users.v1.SignOutRequest
 */
export type SignOutRequest = Message<"api.users.v1.SignOutRequest"> & {
};

/**
 * Describes the message api.users.v1.SignOutRequest.
 * Use `create(SignOutRequestSchema)` to create a new message.
 */
export const SignOutRequestSchema: GenMessage<SignOutRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 4);

/**
 * @generated from message api.users.v1.SignOutResponse
 */
export type SignOutResponse = Message<"api.users.v1.SignOutResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.SignOutResponse.
 * Use `create(SignOutResponseSchema)` to create a new message.
 */
export const SignOutResponseSchema: GenMessage<SignOutResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 5);

/**
 * @generated from message api.users.v1.SendSignupEmailVerificationCodeRequest
 */
export type SendSignupEmailVerificationCodeRequest = Message<"api.users.v1.SendSignupEmailVerificationCodeRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;
};

/**
 * Describes the message api.users.v1.SendSignupEmailVerificationCodeRequest.
 * Use `create(SendSignupEmailVerificationCodeRequestSchema)` to create a new message.
 */
export const SendSignupEmailVerificationCodeRequestSchema: GenMessage<SendSignupEmailVerificationCodeRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 6);

/**
 * @generated from message api.users.v1.SendSignupEmailVerificationCodeResponse
 */
export type SendSignupEmailVerificationCodeResponse = Message<"api.users.v1.SendSignupEmailVerificationCodeResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.SendSignupEmailVerificationCodeResponse.
 * Use `create(SendSignupEmailVerificationCodeResponseSchema)` to create a new message.
 */
export const SendSignupEmailVerificationCodeResponseSchema: GenMessage<SendSignupEmailVerificationCodeResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 7);

/**
 * @generated from message api.users.v1.VerifySignupEmailRequest
 */
export type VerifySignupEmailRequest = Message<"api.users.v1.VerifySignupEmailRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: string verification_code = 2;
   */
  verificationCode: string;
};

/**
 * Describes the message api.users.v1.VerifySignupEmailRequest.
 * Use `create(VerifySignupEmailRequestSchema)` to create a new message.
 */
export const VerifySignupEmailRequestSchema: GenMessage<VerifySignupEmailRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 8);

/**
 * @generated from message api.users.v1.VerifySignupEmailResponse
 */
export type VerifySignupEmailResponse = Message<"api.users.v1.VerifySignupEmailResponse"> & {
  /**
   * @generated from field: string token = 1;
   */
  token: string;
};

/**
 * Describes the message api.users.v1.VerifySignupEmailResponse.
 * Use `create(VerifySignupEmailResponseSchema)` to create a new message.
 */
export const VerifySignupEmailResponseSchema: GenMessage<VerifySignupEmailResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 9);

/**
 * @generated from message api.users.v1.SignupWithEmailRequest
 */
export type SignupWithEmailRequest = Message<"api.users.v1.SignupWithEmailRequest"> & {
  /**
   * @generated from field: string full_name = 1;
   */
  fullName: string;

  /**
   * @generated from field: string password = 2;
   */
  password: string;

  /**
   * @generated from field: optional google.protobuf.Timestamp date_of_birth = 3;
   */
  dateOfBirth?: Timestamp;

  /**
   * @generated from field: string token = 4;
   */
  token: string;
};

/**
 * Describes the message api.users.v1.SignupWithEmailRequest.
 * Use `create(SignupWithEmailRequestSchema)` to create a new message.
 */
export const SignupWithEmailRequestSchema: GenMessage<SignupWithEmailRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 10);

/**
 * @generated from message api.users.v1.SignupWithEmailResponse
 */
export type SignupWithEmailResponse = Message<"api.users.v1.SignupWithEmailResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.SignupWithEmailResponse.
 * Use `create(SignupWithEmailResponseSchema)` to create a new message.
 */
export const SignupWithEmailResponseSchema: GenMessage<SignupWithEmailResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 11);

/**
 * @generated from message api.users.v1.SignInWithEmailRequest
 */
export type SignInWithEmailRequest = Message<"api.users.v1.SignInWithEmailRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: string password = 2;
   */
  password: string;
};

/**
 * Describes the message api.users.v1.SignInWithEmailRequest.
 * Use `create(SignInWithEmailRequestSchema)` to create a new message.
 */
export const SignInWithEmailRequestSchema: GenMessage<SignInWithEmailRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 12);

/**
 * @generated from message api.users.v1.SignInWithEmailResponse
 */
export type SignInWithEmailResponse = Message<"api.users.v1.SignInWithEmailResponse"> & {
  /**
   * @generated from field: string access_token = 1;
   */
  accessToken: string;

  /**
   * @generated from field: string refresh_token = 2;
   */
  refreshToken: string;
};

/**
 * Describes the message api.users.v1.SignInWithEmailResponse.
 * Use `create(SignInWithEmailResponseSchema)` to create a new message.
 */
export const SignInWithEmailResponseSchema: GenMessage<SignInWithEmailResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 13);

/**
 * @generated from message api.users.v1.SendForgotPasswordEmailRequest
 */
export type SendForgotPasswordEmailRequest = Message<"api.users.v1.SendForgotPasswordEmailRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;
};

/**
 * Describes the message api.users.v1.SendForgotPasswordEmailRequest.
 * Use `create(SendForgotPasswordEmailRequestSchema)` to create a new message.
 */
export const SendForgotPasswordEmailRequestSchema: GenMessage<SendForgotPasswordEmailRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 14);

/**
 * @generated from message api.users.v1.SendForgotPasswordEmailResponse
 */
export type SendForgotPasswordEmailResponse = Message<"api.users.v1.SendForgotPasswordEmailResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.SendForgotPasswordEmailResponse.
 * Use `create(SendForgotPasswordEmailResponseSchema)` to create a new message.
 */
export const SendForgotPasswordEmailResponseSchema: GenMessage<SendForgotPasswordEmailResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 15);

/**
 * @generated from message api.users.v1.ResetPasswordRequest
 */
export type ResetPasswordRequest = Message<"api.users.v1.ResetPasswordRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: string verification_code = 2;
   */
  verificationCode: string;

  /**
   * @generated from field: string new_password = 3;
   */
  newPassword: string;
};

/**
 * Describes the message api.users.v1.ResetPasswordRequest.
 * Use `create(ResetPasswordRequestSchema)` to create a new message.
 */
export const ResetPasswordRequestSchema: GenMessage<ResetPasswordRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 16);

/**
 * @generated from message api.users.v1.ResetPasswordResponse
 */
export type ResetPasswordResponse = Message<"api.users.v1.ResetPasswordResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.ResetPasswordResponse.
 * Use `create(ResetPasswordResponseSchema)` to create a new message.
 */
export const ResetPasswordResponseSchema: GenMessage<ResetPasswordResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 17);

/**
 * @generated from message api.users.v1.ChangePasswordRequest
 */
export type ChangePasswordRequest = Message<"api.users.v1.ChangePasswordRequest"> & {
  /**
   * @generated from field: string old_password = 1;
   */
  oldPassword: string;

  /**
   * @generated from field: string new_password = 2;
   */
  newPassword: string;
};

/**
 * Describes the message api.users.v1.ChangePasswordRequest.
 * Use `create(ChangePasswordRequestSchema)` to create a new message.
 */
export const ChangePasswordRequestSchema: GenMessage<ChangePasswordRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 18);

/**
 * @generated from message api.users.v1.ChangePasswordResponse
 */
export type ChangePasswordResponse = Message<"api.users.v1.ChangePasswordResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.ChangePasswordResponse.
 * Use `create(ChangePasswordResponseSchema)` to create a new message.
 */
export const ChangePasswordResponseSchema: GenMessage<ChangePasswordResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 19);

/**
 * @generated from message api.users.v1.ChangeEmailVerificationRequest
 */
export type ChangeEmailVerificationRequest = Message<"api.users.v1.ChangeEmailVerificationRequest"> & {
};

/**
 * Describes the message api.users.v1.ChangeEmailVerificationRequest.
 * Use `create(ChangeEmailVerificationRequestSchema)` to create a new message.
 */
export const ChangeEmailVerificationRequestSchema: GenMessage<ChangeEmailVerificationRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 20);

/**
 * @generated from message api.users.v1.ChangeEmailVerificationResponse
 */
export type ChangeEmailVerificationResponse = Message<"api.users.v1.ChangeEmailVerificationResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.ChangeEmailVerificationResponse.
 * Use `create(ChangeEmailVerificationResponseSchema)` to create a new message.
 */
export const ChangeEmailVerificationResponseSchema: GenMessage<ChangeEmailVerificationResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 21);

/**
 * @generated from message api.users.v1.VerifyChangeEmailRequest
 */
export type VerifyChangeEmailRequest = Message<"api.users.v1.VerifyChangeEmailRequest"> & {
  /**
   * @generated from field: string verification_code = 1;
   */
  verificationCode: string;
};

/**
 * Describes the message api.users.v1.VerifyChangeEmailRequest.
 * Use `create(VerifyChangeEmailRequestSchema)` to create a new message.
 */
export const VerifyChangeEmailRequestSchema: GenMessage<VerifyChangeEmailRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 22);

/**
 * @generated from message api.users.v1.VerifyChangeEmailResponse
 */
export type VerifyChangeEmailResponse = Message<"api.users.v1.VerifyChangeEmailResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string token = 2;
   */
  token: string;
};

/**
 * Describes the message api.users.v1.VerifyChangeEmailResponse.
 * Use `create(VerifyChangeEmailResponseSchema)` to create a new message.
 */
export const VerifyChangeEmailResponseSchema: GenMessage<VerifyChangeEmailResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 23);

/**
 * @generated from message api.users.v1.SendVerifyNewEmailRequest
 */
export type SendVerifyNewEmailRequest = Message<"api.users.v1.SendVerifyNewEmailRequest"> & {
  /**
   * @generated from field: string new_email = 2;
   */
  newEmail: string;

  /**
   * @generated from field: string token = 3;
   */
  token: string;
};

/**
 * Describes the message api.users.v1.SendVerifyNewEmailRequest.
 * Use `create(SendVerifyNewEmailRequestSchema)` to create a new message.
 */
export const SendVerifyNewEmailRequestSchema: GenMessage<SendVerifyNewEmailRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 24);

/**
 * @generated from message api.users.v1.SendVerifyNewEmailResponse
 */
export type SendVerifyNewEmailResponse = Message<"api.users.v1.SendVerifyNewEmailResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.SendVerifyNewEmailResponse.
 * Use `create(SendVerifyNewEmailResponseSchema)` to create a new message.
 */
export const SendVerifyNewEmailResponseSchema: GenMessage<SendVerifyNewEmailResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 25);

/**
 * @generated from message api.users.v1.VerifyNewEmailWithCodeRequest
 */
export type VerifyNewEmailWithCodeRequest = Message<"api.users.v1.VerifyNewEmailWithCodeRequest"> & {
  /**
   * @generated from field: string verification_code = 1;
   */
  verificationCode: string;

  /**
   * @generated from field: string new_email = 2;
   */
  newEmail: string;
};

/**
 * Describes the message api.users.v1.VerifyNewEmailWithCodeRequest.
 * Use `create(VerifyNewEmailWithCodeRequestSchema)` to create a new message.
 */
export const VerifyNewEmailWithCodeRequestSchema: GenMessage<VerifyNewEmailWithCodeRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 26);

/**
 * @generated from message api.users.v1.VerifyNewEmailRequest
 */
export type VerifyNewEmailRequest = Message<"api.users.v1.VerifyNewEmailRequest"> & {
  /**
   * @generated from field: optional api.users.v1.VerifyNewEmailWithCodeRequest verify_new_email_with_code = 1;
   */
  verifyNewEmailWithCode?: VerifyNewEmailWithCodeRequest;

  /**
   * @generated from field: optional string token = 2;
   */
  token?: string;
};

/**
 * Describes the message api.users.v1.VerifyNewEmailRequest.
 * Use `create(VerifyNewEmailRequestSchema)` to create a new message.
 */
export const VerifyNewEmailRequestSchema: GenMessage<VerifyNewEmailRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 27);

/**
 * @generated from message api.users.v1.VerifyNewEmailResponse
 */
export type VerifyNewEmailResponse = Message<"api.users.v1.VerifyNewEmailResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.VerifyNewEmailResponse.
 * Use `create(VerifyNewEmailResponseSchema)` to create a new message.
 */
export const VerifyNewEmailResponseSchema: GenMessage<VerifyNewEmailResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 28);

/**
 * @generated from message api.users.v1.UpdateUserDetailsRequest
 */
export type UpdateUserDetailsRequest = Message<"api.users.v1.UpdateUserDetailsRequest"> & {
  /**
   * @generated from field: string full_name = 1;
   */
  fullName: string;

  /**
   * @generated from field: optional google.protobuf.Timestamp date_of_birth = 2;
   */
  dateOfBirth?: Timestamp;
};

/**
 * Describes the message api.users.v1.UpdateUserDetailsRequest.
 * Use `create(UpdateUserDetailsRequestSchema)` to create a new message.
 */
export const UpdateUserDetailsRequestSchema: GenMessage<UpdateUserDetailsRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 29);

/**
 * @generated from message api.users.v1.UpdateUserDetailsResponse
 */
export type UpdateUserDetailsResponse = Message<"api.users.v1.UpdateUserDetailsResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.UpdateUserDetailsResponse.
 * Use `create(UpdateUserDetailsResponseSchema)` to create a new message.
 */
export const UpdateUserDetailsResponseSchema: GenMessage<UpdateUserDetailsResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 30);

/**
 * @generated from message api.users.v1.UpdateUserImageRequest
 */
export type UpdateUserImageRequest = Message<"api.users.v1.UpdateUserImageRequest"> & {
  /**
   * @generated from field: string image = 1;
   */
  image: string;
};

/**
 * Describes the message api.users.v1.UpdateUserImageRequest.
 * Use `create(UpdateUserImageRequestSchema)` to create a new message.
 */
export const UpdateUserImageRequestSchema: GenMessage<UpdateUserImageRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 31);

/**
 * @generated from message api.users.v1.UpdateUserImageResponse
 */
export type UpdateUserImageResponse = Message<"api.users.v1.UpdateUserImageResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.UpdateUserImageResponse.
 * Use `create(UpdateUserImageResponseSchema)` to create a new message.
 */
export const UpdateUserImageResponseSchema: GenMessage<UpdateUserImageResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 32);

/**
 * @generated from message api.users.v1.ListSessionsRequest
 */
export type ListSessionsRequest = Message<"api.users.v1.ListSessionsRequest"> & {
};

/**
 * Describes the message api.users.v1.ListSessionsRequest.
 * Use `create(ListSessionsRequestSchema)` to create a new message.
 */
export const ListSessionsRequestSchema: GenMessage<ListSessionsRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 33);

/**
 * @generated from message api.users.v1.Session
 */
export type Session = Message<"api.users.v1.Session"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string user_agent = 2;
   */
  userAgent: string;

  /**
   * @generated from field: string ip_address = 3;
   */
  ipAddress: string;

  /**
   * @generated from field: string created_at = 4;
   */
  createdAt: string;

  /**
   * @generated from field: bool isCurrent = 5;
   */
  isCurrent: boolean;
};

/**
 * Describes the message api.users.v1.Session.
 * Use `create(SessionSchema)` to create a new message.
 */
export const SessionSchema: GenMessage<Session> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 34);

/**
 * @generated from message api.users.v1.ListSessionsResponse
 */
export type ListSessionsResponse = Message<"api.users.v1.ListSessionsResponse"> & {
  /**
   * @generated from field: repeated api.users.v1.Session sessions = 1;
   */
  sessions: Session[];
};

/**
 * Describes the message api.users.v1.ListSessionsResponse.
 * Use `create(ListSessionsResponseSchema)` to create a new message.
 */
export const ListSessionsResponseSchema: GenMessage<ListSessionsResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 35);

/**
 * @generated from message api.users.v1.RevokeSessionsRequest
 */
export type RevokeSessionsRequest = Message<"api.users.v1.RevokeSessionsRequest"> & {
  /**
   * @generated from field: repeated string session_ids = 1;
   */
  sessionIds: string[];
};

/**
 * Describes the message api.users.v1.RevokeSessionsRequest.
 * Use `create(RevokeSessionsRequestSchema)` to create a new message.
 */
export const RevokeSessionsRequestSchema: GenMessage<RevokeSessionsRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 36);

/**
 * @generated from message api.users.v1.RevokeSessionsResponse
 */
export type RevokeSessionsResponse = Message<"api.users.v1.RevokeSessionsResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.RevokeSessionsResponse.
 * Use `create(RevokeSessionsResponseSchema)` to create a new message.
 */
export const RevokeSessionsResponseSchema: GenMessage<RevokeSessionsResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 37);

/**
 * @generated from message api.users.v1.RevokeOtherSessionsRequest
 */
export type RevokeOtherSessionsRequest = Message<"api.users.v1.RevokeOtherSessionsRequest"> & {
};

/**
 * Describes the message api.users.v1.RevokeOtherSessionsRequest.
 * Use `create(RevokeOtherSessionsRequestSchema)` to create a new message.
 */
export const RevokeOtherSessionsRequestSchema: GenMessage<RevokeOtherSessionsRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 38);

/**
 * @generated from message api.users.v1.RevokeOtherSessionsResponse
 */
export type RevokeOtherSessionsResponse = Message<"api.users.v1.RevokeOtherSessionsResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.RevokeOtherSessionsResponse.
 * Use `create(RevokeOtherSessionsResponseSchema)` to create a new message.
 */
export const RevokeOtherSessionsResponseSchema: GenMessage<RevokeOtherSessionsResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 39);

/**
 * @generated from message api.users.v1.LinkSocialRequest
 */
export type LinkSocialRequest = Message<"api.users.v1.LinkSocialRequest"> & {
  /**
   * @generated from field: string provider = 1;
   */
  provider: string;

  /**
   * @generated from field: string code = 2;
   */
  code: string;
};

/**
 * Describes the message api.users.v1.LinkSocialRequest.
 * Use `create(LinkSocialRequestSchema)` to create a new message.
 */
export const LinkSocialRequestSchema: GenMessage<LinkSocialRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 40);

/**
 * @generated from message api.users.v1.LinkSocialResponse
 */
export type LinkSocialResponse = Message<"api.users.v1.LinkSocialResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.LinkSocialResponse.
 * Use `create(LinkSocialResponseSchema)` to create a new message.
 */
export const LinkSocialResponseSchema: GenMessage<LinkSocialResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 41);

/**
 * @generated from message api.users.v1.ListAccountsRequest
 */
export type ListAccountsRequest = Message<"api.users.v1.ListAccountsRequest"> & {
};

/**
 * Describes the message api.users.v1.ListAccountsRequest.
 * Use `create(ListAccountsRequestSchema)` to create a new message.
 */
export const ListAccountsRequestSchema: GenMessage<ListAccountsRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 42);

/**
 * @generated from message api.users.v1.ListAccountsResponse
 */
export type ListAccountsResponse = Message<"api.users.v1.ListAccountsResponse"> & {
  /**
   * @generated from field: repeated api.users.v1.Account accounts = 1;
   */
  accounts: Account[];
};

/**
 * Describes the message api.users.v1.ListAccountsResponse.
 * Use `create(ListAccountsResponseSchema)` to create a new message.
 */
export const ListAccountsResponseSchema: GenMessage<ListAccountsResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 43);

/**
 * @generated from message api.users.v1.Account
 */
export type Account = Message<"api.users.v1.Account"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string provider = 2;
   */
  provider: string;

  /**
   * @generated from field: string user_id = 3;
   */
  userId: string;
};

/**
 * Describes the message api.users.v1.Account.
 * Use `create(AccountSchema)` to create a new message.
 */
export const AccountSchema: GenMessage<Account> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 44);

/**
 * @generated from message api.users.v1.UnlinkAccountRequest
 */
export type UnlinkAccountRequest = Message<"api.users.v1.UnlinkAccountRequest"> & {
  /**
   * @generated from field: string account_id = 1;
   */
  accountId: string;
};

/**
 * Describes the message api.users.v1.UnlinkAccountRequest.
 * Use `create(UnlinkAccountRequestSchema)` to create a new message.
 */
export const UnlinkAccountRequestSchema: GenMessage<UnlinkAccountRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 45);

/**
 * @generated from message api.users.v1.UnlinkAccountResponse
 */
export type UnlinkAccountResponse = Message<"api.users.v1.UnlinkAccountResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.users.v1.UnlinkAccountResponse.
 * Use `create(UnlinkAccountResponseSchema)` to create a new message.
 */
export const UnlinkAccountResponseSchema: GenMessage<UnlinkAccountResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 46);

/**
 * @generated from message api.users.v1.RefreshTokenRequest
 */
export type RefreshTokenRequest = Message<"api.users.v1.RefreshTokenRequest"> & {
  /**
   * @generated from field: string refresh_token = 1;
   */
  refreshToken: string;
};

/**
 * Describes the message api.users.v1.RefreshTokenRequest.
 * Use `create(RefreshTokenRequestSchema)` to create a new message.
 */
export const RefreshTokenRequestSchema: GenMessage<RefreshTokenRequest> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 47);

/**
 * @generated from message api.users.v1.RefreshTokenResponse
 */
export type RefreshTokenResponse = Message<"api.users.v1.RefreshTokenResponse"> & {
  /**
   * @generated from field: string access_token = 1;
   */
  accessToken: string;

  /**
   * @generated from field: string refresh_token = 2;
   */
  refreshToken: string;
};

/**
 * Describes the message api.users.v1.RefreshTokenResponse.
 * Use `create(RefreshTokenResponseSchema)` to create a new message.
 */
export const RefreshTokenResponseSchema: GenMessage<RefreshTokenResponse> = /*@__PURE__*/
  messageDesc(file_users_v1_auth, 48);

/**
 * @generated from service api.users.v1.AuthService
 */
export const AuthService: GenService<{
  /**
   * @generated from rpc api.users.v1.AuthService.SignInWithEmail
   */
  signInWithEmail: {
    methodKind: "unary";
    input: typeof SignInWithEmailRequestSchema;
    output: typeof SignInWithEmailResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.RefreshToken
   */
  refreshToken: {
    methodKind: "unary";
    input: typeof RefreshTokenRequestSchema;
    output: typeof RefreshTokenResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.SignInWithSocial
   */
  signInWithSocial: {
    methodKind: "unary";
    input: typeof SignInWithSocialRequestSchema;
    output: typeof SignInWithSocialResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.SignOut
   */
  signOut: {
    methodKind: "unary";
    input: typeof SignOutRequestSchema;
    output: typeof SignOutResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.GetSession
   */
  getSession: {
    methodKind: "unary";
    input: typeof GetSessionRequestSchema;
    output: typeof GetSessionResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.SendSignupEmailVerificationCode
   */
  sendSignupEmailVerificationCode: {
    methodKind: "unary";
    input: typeof SendSignupEmailVerificationCodeRequestSchema;
    output: typeof SendSignupEmailVerificationCodeResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.VerifySignupEmail
   */
  verifySignupEmail: {
    methodKind: "unary";
    input: typeof VerifySignupEmailRequestSchema;
    output: typeof VerifySignupEmailResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.SignupWithEmail
   */
  signupWithEmail: {
    methodKind: "unary";
    input: typeof SignupWithEmailRequestSchema;
    output: typeof SignupWithEmailResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.SendForgotPasswordEmail
   */
  sendForgotPasswordEmail: {
    methodKind: "unary";
    input: typeof SendForgotPasswordEmailRequestSchema;
    output: typeof SendForgotPasswordEmailResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.ResetPassword
   */
  resetPassword: {
    methodKind: "unary";
    input: typeof ResetPasswordRequestSchema;
    output: typeof ResetPasswordResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.ChangePassword
   */
  changePassword: {
    methodKind: "unary";
    input: typeof ChangePasswordRequestSchema;
    output: typeof ChangePasswordResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.ChangeEmailVerification
   */
  changeEmailVerification: {
    methodKind: "unary";
    input: typeof ChangeEmailVerificationRequestSchema;
    output: typeof ChangeEmailVerificationResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.VerifyChangeEmail
   */
  verifyChangeEmail: {
    methodKind: "unary";
    input: typeof VerifyChangeEmailRequestSchema;
    output: typeof VerifyChangeEmailResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.SendVerifyNewEmail
   */
  sendVerifyNewEmail: {
    methodKind: "unary";
    input: typeof SendVerifyNewEmailRequestSchema;
    output: typeof SendVerifyNewEmailResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.VerifyNewEmail
   */
  verifyNewEmail: {
    methodKind: "unary";
    input: typeof VerifyNewEmailRequestSchema;
    output: typeof VerifyNewEmailResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.UpdateUserDetails
   */
  updateUserDetails: {
    methodKind: "unary";
    input: typeof UpdateUserDetailsRequestSchema;
    output: typeof UpdateUserDetailsResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.UpdateUserImage
   */
  updateUserImage: {
    methodKind: "unary";
    input: typeof UpdateUserImageRequestSchema;
    output: typeof UpdateUserImageResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.ListSessions
   */
  listSessions: {
    methodKind: "unary";
    input: typeof ListSessionsRequestSchema;
    output: typeof ListSessionsResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.RevokeSessions
   */
  revokeSessions: {
    methodKind: "unary";
    input: typeof RevokeSessionsRequestSchema;
    output: typeof RevokeSessionsResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.RevokeOtherSessions
   */
  revokeOtherSessions: {
    methodKind: "unary";
    input: typeof RevokeOtherSessionsRequestSchema;
    output: typeof RevokeOtherSessionsResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.LinkSocial
   */
  linkSocial: {
    methodKind: "unary";
    input: typeof LinkSocialRequestSchema;
    output: typeof LinkSocialResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.ListAccounts
   */
  listAccounts: {
    methodKind: "unary";
    input: typeof ListAccountsRequestSchema;
    output: typeof ListAccountsResponseSchema;
  },
  /**
   * @generated from rpc api.users.v1.AuthService.UnlinkAccount
   */
  unlinkAccount: {
    methodKind: "unary";
    input: typeof UnlinkAccountRequestSchema;
    output: typeof UnlinkAccountResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_users_v1_auth, 0);

