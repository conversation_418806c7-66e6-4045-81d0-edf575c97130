// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file events/v1/eventvote.proto (package api.events.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file events/v1/eventvote.proto.
 */
export const file_events_v1_eventvote: GenFile = /*@__PURE__*/
  fileDesc("ChlldmVudHMvdjEvZXZlbnR2b3RlLnByb3RvEg1hcGkuZXZlbnRzLnYxIloKDkFkZFZvdGVSZXF1ZXN0Eh4KFmV2ZW50X3BhcnRpY2lwYXRpb25faWQYASABKAMSGAoQZnJvbV9kYWlseV9wb2ludBgCIAEoCBIOCgZwb2ludHMYAyABKAUiMQodR2V0RGFpbHlQb2ludEF2YWlsYWJsZVJlcXVlc3QSEAoIZXZlbnRfaWQYASABKAMiMwoeR2V0RGFpbHlQb2ludEF2YWlsYWJsZVJlc3BvbnNlEhEKCWF2YWlsYWJsZRgBIAEoCCJbChhHaXZlUGxhdGZvcm1Qb2ludFJlcXVlc3QSHgoWZXZlbnRfcGFydGljaXBhdGlvbl9pZBgBIAEoAxIPCgdyZW1hcmtzGAIgASgJEg4KBnBvaW50cxgDIAEoAyIyChBHZXRQbGF0ZnJvbVBvaW50Eh4KFmV2ZW50X3BhcnRpY2lwYXRpb25faWQYASABKAMiMwoPQWRkVm90ZVJlc3BvbnNlEg8KB3N1Y2Nlc3MYASABKAgSDwoHbWVzc2FnZRgCIAEoCSI9ChlHaXZlUGxhdGZvcm1Qb2ludFJlc3BvbnNlEg8KB3N1Y2Nlc3MYASABKAgSDwoHbWVzc2FnZRgCIAEoCTLRAwoQRXZlbnRWb3RlU2VydmljZRJQCgdBZGRWb3RlEh0uYXBpLmV2ZW50cy52MS5BZGRWb3RlUmVxdWVzdBoeLmFwaS5ldmVudHMudjEuQWRkVm90ZVJlc3BvbnNlIgaCtRgCCAESfQoWR2V0RGFpbHlQb2ludEF2YWlsYWJsZRIsLmFwaS5ldmVudHMudjEuR2V0RGFpbHlQb2ludEF2YWlsYWJsZVJlcXVlc3QaLS5hcGkuZXZlbnRzLnYxLkdldERhaWx5UG9pbnRBdmFpbGFibGVSZXNwb25zZSIGgrUYAggBEnAKEUdpdmVQbGF0Zm9ybVBvaW50EicuYXBpLmV2ZW50cy52MS5HaXZlUGxhdGZvcm1Qb2ludFJlcXVlc3QaKC5hcGkuZXZlbnRzLnYxLkdpdmVQbGF0Zm9ybVBvaW50UmVzcG9uc2UiCIK1GAQIARABEnoKJEdldFBsYXRmcm9tUG9pbnRPZkV2ZW50UGFydGljaXBhdGlvbhIfLmFwaS5ldmVudHMudjEuR2V0UGxhdGZyb21Qb2ludBonLmFwaS5ldmVudHMudjEuR2l2ZVBsYXRmb3JtUG9pbnRSZXF1ZXN0IgiCtRgECAEQAUIyWjBnaXRodWIuY29tL25zcC1pbmMvdnR1YmVyL2FwaS9ldmVudHMvdjE7ZXZlbnRzdjFiBnByb3RvMw", [file_authz_v1_authz, file_shared_v1_generic, file_shared_v1_pagination, file_shared_v1_profile, file_shared_v1_social_media_links]);

/**
 * @generated from message api.events.v1.AddVoteRequest
 */
export type AddVoteRequest = Message<"api.events.v1.AddVoteRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 event_participation_id = 1;
   */
  eventParticipationId: bigint;

  /**
   * @generated from field: bool from_daily_point = 2;
   */
  fromDailyPoint: boolean;

  /**
   * @generated from field: int32 points = 3;
   */
  points: number;
};

/**
 * Describes the message api.events.v1.AddVoteRequest.
 * Use `create(AddVoteRequestSchema)` to create a new message.
 */
export const AddVoteRequestSchema: GenMessage<AddVoteRequest> = /*@__PURE__*/
  messageDesc(file_events_v1_eventvote, 0);

/**
 * @generated from message api.events.v1.GetDailyPointAvailableRequest
 */
export type GetDailyPointAvailableRequest = Message<"api.events.v1.GetDailyPointAvailableRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 event_id = 1;
   */
  eventId: bigint;
};

/**
 * Describes the message api.events.v1.GetDailyPointAvailableRequest.
 * Use `create(GetDailyPointAvailableRequestSchema)` to create a new message.
 */
export const GetDailyPointAvailableRequestSchema: GenMessage<GetDailyPointAvailableRequest> = /*@__PURE__*/
  messageDesc(file_events_v1_eventvote, 1);

/**
 * @generated from message api.events.v1.GetDailyPointAvailableResponse
 */
export type GetDailyPointAvailableResponse = Message<"api.events.v1.GetDailyPointAvailableResponse"> & {
  /**
   * @generated from field: bool available = 1;
   */
  available: boolean;
};

/**
 * Describes the message api.events.v1.GetDailyPointAvailableResponse.
 * Use `create(GetDailyPointAvailableResponseSchema)` to create a new message.
 */
export const GetDailyPointAvailableResponseSchema: GenMessage<GetDailyPointAvailableResponse> = /*@__PURE__*/
  messageDesc(file_events_v1_eventvote, 2);

/**
 * @generated from message api.events.v1.GivePlatformPointRequest
 */
export type GivePlatformPointRequest = Message<"api.events.v1.GivePlatformPointRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 event_participation_id = 1;
   */
  eventParticipationId: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string remarks = 2;
   */
  remarks: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 points = 3;
   */
  points: bigint;
};

/**
 * Describes the message api.events.v1.GivePlatformPointRequest.
 * Use `create(GivePlatformPointRequestSchema)` to create a new message.
 */
export const GivePlatformPointRequestSchema: GenMessage<GivePlatformPointRequest> = /*@__PURE__*/
  messageDesc(file_events_v1_eventvote, 3);

/**
 * @generated from message api.events.v1.GetPlatfromPoint
 */
export type GetPlatfromPoint = Message<"api.events.v1.GetPlatfromPoint"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 event_participation_id = 1;
   */
  eventParticipationId: bigint;
};

/**
 * Describes the message api.events.v1.GetPlatfromPoint.
 * Use `create(GetPlatfromPointSchema)` to create a new message.
 */
export const GetPlatfromPointSchema: GenMessage<GetPlatfromPoint> = /*@__PURE__*/
  messageDesc(file_events_v1_eventvote, 4);

/**
 * @generated from message api.events.v1.AddVoteResponse
 */
export type AddVoteResponse = Message<"api.events.v1.AddVoteResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.events.v1.AddVoteResponse.
 * Use `create(AddVoteResponseSchema)` to create a new message.
 */
export const AddVoteResponseSchema: GenMessage<AddVoteResponse> = /*@__PURE__*/
  messageDesc(file_events_v1_eventvote, 5);

/**
 * @generated from message api.events.v1.GivePlatformPointResponse
 */
export type GivePlatformPointResponse = Message<"api.events.v1.GivePlatformPointResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.events.v1.GivePlatformPointResponse.
 * Use `create(GivePlatformPointResponseSchema)` to create a new message.
 */
export const GivePlatformPointResponseSchema: GenMessage<GivePlatformPointResponse> = /*@__PURE__*/
  messageDesc(file_events_v1_eventvote, 6);

/**
 * @generated from service api.events.v1.EventVoteService
 */
export const EventVoteService: GenService<{
  /**
   * @generated from rpc api.events.v1.EventVoteService.AddVote
   */
  addVote: {
    methodKind: "unary";
    input: typeof AddVoteRequestSchema;
    output: typeof AddVoteResponseSchema;
  },
  /**
   * @generated from rpc api.events.v1.EventVoteService.GetDailyPointAvailable
   */
  getDailyPointAvailable: {
    methodKind: "unary";
    input: typeof GetDailyPointAvailableRequestSchema;
    output: typeof GetDailyPointAvailableResponseSchema;
  },
  /**
   * @generated from rpc api.events.v1.EventVoteService.GivePlatformPoint
   */
  givePlatformPoint: {
    methodKind: "unary";
    input: typeof GivePlatformPointRequestSchema;
    output: typeof GivePlatformPointResponseSchema;
  },
  /**
   * @generated from rpc api.events.v1.EventVoteService.GetPlatfromPointOfEventParticipation
   */
  getPlatfromPointOfEventParticipation: {
    methodKind: "unary";
    input: typeof GetPlatfromPointSchema;
    output: typeof GivePlatformPointRequestSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_events_v1_eventvote, 0);

