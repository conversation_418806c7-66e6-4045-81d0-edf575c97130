// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file vtubers/v1/vtuberplan.proto (package api.vtubers.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { GenericResponseSchema } from "../../shared/v1/generic_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file vtubers/v1/vtuberplan.proto.
 */
export const file_vtubers_v1_vtuberplan: GenFile = /*@__PURE__*/
  fileDesc("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", [file_authz_v1_authz, file_google_protobuf_timestamp, file_shared_v1_generic, file_shared_v1_pagination, file_shared_v1_profile, file_shared_v1_social_media_links]);

/**
 * @generated from message api.vtubers.v1.AddVtuberPlanRequest
 */
export type AddVtuberPlanRequest = Message<"api.vtubers.v1.AddVtuberPlanRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @gotag: validate:"gte=1"
   *
   * @generated from field: int32 price = 4;
   */
  price: number;

  /**
   * @gotag: validate:"gte=1"
   *
   * @generated from field: int32 index = 5;
   */
  index: number;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string short_description = 6;
   */
  shortDescription: string;

  /**
   * @gotag: validate:"gte=1"
   *
   * @generated from field: int32 annual_price = 7;
   */
  annualPrice: number;
};

/**
 * Describes the message api.vtubers.v1.AddVtuberPlanRequest.
 * Use `create(AddVtuberPlanRequestSchema)` to create a new message.
 */
export const AddVtuberPlanRequestSchema: GenMessage<AddVtuberPlanRequest> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberplan, 0);

/**
 * @generated from message api.vtubers.v1.AddVtuberPlanResponse
 */
export type AddVtuberPlanResponse = Message<"api.vtubers.v1.AddVtuberPlanResponse"> & {
  /**
   * @generated from field: api.vtubers.v1.VtuberPlan data = 1;
   */
  data?: VtuberPlan;
};

/**
 * Describes the message api.vtubers.v1.AddVtuberPlanResponse.
 * Use `create(AddVtuberPlanResponseSchema)` to create a new message.
 */
export const AddVtuberPlanResponseSchema: GenMessage<AddVtuberPlanResponse> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberplan, 1);

/**
 * @generated from message api.vtubers.v1.VtuberPlan
 */
export type VtuberPlan = Message<"api.vtubers.v1.VtuberPlan"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: int32 price = 4;
   */
  price: number;

  /**
   * @generated from field: int64 vtuber_id = 5;
   */
  vtuberId: bigint;

  /**
   * @generated from field: int32 index = 6;
   */
  index: number;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 7;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: bool is_subscribed = 8;
   */
  isSubscribed: boolean;

  /**
   * @generated from field: string short_description = 9;
   */
  shortDescription: string;

  /**
   * @generated from field: int32 annual_price = 10;
   */
  annualPrice: number;
};

/**
 * Describes the message api.vtubers.v1.VtuberPlan.
 * Use `create(VtuberPlanSchema)` to create a new message.
 */
export const VtuberPlanSchema: GenMessage<VtuberPlan> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberplan, 2);

/**
 * @generated from message api.vtubers.v1.GetAllVtuberPlansByVtuberIdRequest
 */
export type GetAllVtuberPlansByVtuberIdRequest = Message<"api.vtubers.v1.GetAllVtuberPlansByVtuberIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 vtuber_id = 1;
   */
  vtuberId: bigint;
};

/**
 * Describes the message api.vtubers.v1.GetAllVtuberPlansByVtuberIdRequest.
 * Use `create(GetAllVtuberPlansByVtuberIdRequestSchema)` to create a new message.
 */
export const GetAllVtuberPlansByVtuberIdRequestSchema: GenMessage<GetAllVtuberPlansByVtuberIdRequest> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberplan, 3);

/**
 * @generated from message api.vtubers.v1.GetAllVtuberPlansResponse
 */
export type GetAllVtuberPlansResponse = Message<"api.vtubers.v1.GetAllVtuberPlansResponse"> & {
  /**
   * @generated from field: repeated api.vtubers.v1.VtuberPlan VtuberPlan = 1;
   */
  VtuberPlan: VtuberPlan[];
};

/**
 * Describes the message api.vtubers.v1.GetAllVtuberPlansResponse.
 * Use `create(GetAllVtuberPlansResponseSchema)` to create a new message.
 */
export const GetAllVtuberPlansResponseSchema: GenMessage<GetAllVtuberPlansResponse> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberplan, 4);

/**
 * @generated from message api.vtubers.v1.GetVtuberPlanByIdRequest
 */
export type GetVtuberPlanByIdRequest = Message<"api.vtubers.v1.GetVtuberPlanByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.vtubers.v1.GetVtuberPlanByIdRequest.
 * Use `create(GetVtuberPlanByIdRequestSchema)` to create a new message.
 */
export const GetVtuberPlanByIdRequestSchema: GenMessage<GetVtuberPlanByIdRequest> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberplan, 5);

/**
 * @generated from message api.vtubers.v1.GetVtuberPlanByIdResponse
 */
export type GetVtuberPlanByIdResponse = Message<"api.vtubers.v1.GetVtuberPlanByIdResponse"> & {
  /**
   * @generated from field: api.vtubers.v1.VtuberPlan data = 1;
   */
  data?: VtuberPlan;
};

/**
 * Describes the message api.vtubers.v1.GetVtuberPlanByIdResponse.
 * Use `create(GetVtuberPlanByIdResponseSchema)` to create a new message.
 */
export const GetVtuberPlanByIdResponseSchema: GenMessage<GetVtuberPlanByIdResponse> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberplan, 6);

/**
 * @generated from message api.vtubers.v1.DeleteVtuberPlanByIdRequest
 */
export type DeleteVtuberPlanByIdRequest = Message<"api.vtubers.v1.DeleteVtuberPlanByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.vtubers.v1.DeleteVtuberPlanByIdRequest.
 * Use `create(DeleteVtuberPlanByIdRequestSchema)` to create a new message.
 */
export const DeleteVtuberPlanByIdRequestSchema: GenMessage<DeleteVtuberPlanByIdRequest> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberplan, 7);

/**
 * @generated from message api.vtubers.v1.UpdateVtuberPlanByIdRequest
 */
export type UpdateVtuberPlanByIdRequest = Message<"api.vtubers.v1.UpdateVtuberPlanByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 3;
   */
  id: bigint;

  /**
   * @gotag: validate:"gte=1"
   *
   * @generated from field: int32 price = 4;
   */
  price: number;

  /**
   * @gotag: validate:"gte=1"
   *
   * @generated from field: int32 index = 6;
   */
  index: number;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string short_description = 7;
   */
  shortDescription: string;

  /**
   * @gotag: validate:"gte=1"
   *
   * @generated from field: int32 annual_price = 8;
   */
  annualPrice: number;
};

/**
 * Describes the message api.vtubers.v1.UpdateVtuberPlanByIdRequest.
 * Use `create(UpdateVtuberPlanByIdRequestSchema)` to create a new message.
 */
export const UpdateVtuberPlanByIdRequestSchema: GenMessage<UpdateVtuberPlanByIdRequest> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberplan, 8);

/**
 * @generated from service api.vtubers.v1.VtuberPlanService
 */
export const VtuberPlanService: GenService<{
  /**
   * @generated from rpc api.vtubers.v1.VtuberPlanService.AddVtuberPlan
   */
  addVtuberPlan: {
    methodKind: "unary";
    input: typeof AddVtuberPlanRequestSchema;
    output: typeof AddVtuberPlanResponseSchema;
  },
  /**
   * @generated from rpc api.vtubers.v1.VtuberPlanService.GetAllVtuberPlansByVtuberId
   */
  getAllVtuberPlansByVtuberId: {
    methodKind: "unary";
    input: typeof GetAllVtuberPlansByVtuberIdRequestSchema;
    output: typeof GetAllVtuberPlansResponseSchema;
  },
  /**
   * @generated from rpc api.vtubers.v1.VtuberPlanService.GetVtuberPlanById
   */
  getVtuberPlanById: {
    methodKind: "unary";
    input: typeof GetVtuberPlanByIdRequestSchema;
    output: typeof GetVtuberPlanByIdResponseSchema;
  },
  /**
   * @generated from rpc api.vtubers.v1.VtuberPlanService.DeleteVtuberPlanById
   */
  deleteVtuberPlanById: {
    methodKind: "unary";
    input: typeof DeleteVtuberPlanByIdRequestSchema;
    output: typeof GenericResponseSchema;
  },
  /**
   * @generated from rpc api.vtubers.v1.VtuberPlanService.UpdateVtuberPlanById
   */
  updateVtuberPlanById: {
    methodKind: "unary";
    input: typeof UpdateVtuberPlanByIdRequestSchema;
    output: typeof GenericResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_vtubers_v1_vtuberplan, 0);

