// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file vtubers/v1/vtubergallery.proto (package api.vtubers.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { GenericResponseSchema } from "../../shared/v1/generic_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type { PaginationDetails, PaginationRequest } from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file vtubers/v1/vtubergallery.proto.
 */
export const file_vtubers_v1_vtubergallery: GenFile = /*@__PURE__*/
  fileDesc("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", [file_authz_v1_authz, file_google_protobuf_timestamp, file_shared_v1_generic, file_shared_v1_pagination, file_shared_v1_profile, file_shared_v1_social_media_links]);

/**
 * @generated from message api.vtubers.v1.AddVtuberGalleryRequest
 */
export type AddVtuberGalleryRequest = Message<"api.vtubers.v1.AddVtuberGalleryRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string media = 1;
   */
  media: string;

  /**
   * @gotag: validate:"required,oneof=picture video"
   *
   * @generated from field: string media_type = 2;
   */
  mediaType: string;

  /**
   * @generated from field: optional string description = 3;
   */
  description?: string;
};

/**
 * Describes the message api.vtubers.v1.AddVtuberGalleryRequest.
 * Use `create(AddVtuberGalleryRequestSchema)` to create a new message.
 */
export const AddVtuberGalleryRequestSchema: GenMessage<AddVtuberGalleryRequest> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubergallery, 0);

/**
 * @generated from message api.vtubers.v1.VtuberGallery
 */
export type VtuberGallery = Message<"api.vtubers.v1.VtuberGallery"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: int64 vtuber_id = 2;
   */
  vtuberId: bigint;

  /**
   * @generated from field: string media = 3;
   */
  media: string;

  /**
   * @generated from field: string media_type = 4;
   */
  mediaType: string;

  /**
   * @generated from field: optional string description = 5;
   */
  description?: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 6;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 7;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message api.vtubers.v1.VtuberGallery.
 * Use `create(VtuberGallerySchema)` to create a new message.
 */
export const VtuberGallerySchema: GenMessage<VtuberGallery> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubergallery, 1);

/**
 * @generated from message api.vtubers.v1.AddVtuberGalleryResponse
 */
export type AddVtuberGalleryResponse = Message<"api.vtubers.v1.AddVtuberGalleryResponse"> & {
  /**
   * @generated from field: api.vtubers.v1.VtuberGallery data = 1;
   */
  data?: VtuberGallery;
};

/**
 * Describes the message api.vtubers.v1.AddVtuberGalleryResponse.
 * Use `create(AddVtuberGalleryResponseSchema)` to create a new message.
 */
export const AddVtuberGalleryResponseSchema: GenMessage<AddVtuberGalleryResponse> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubergallery, 2);

/**
 * @generated from message api.vtubers.v1.DeleteVtuberGalleryByIdRequest
 */
export type DeleteVtuberGalleryByIdRequest = Message<"api.vtubers.v1.DeleteVtuberGalleryByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.vtubers.v1.DeleteVtuberGalleryByIdRequest.
 * Use `create(DeleteVtuberGalleryByIdRequestSchema)` to create a new message.
 */
export const DeleteVtuberGalleryByIdRequestSchema: GenMessage<DeleteVtuberGalleryByIdRequest> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubergallery, 3);

/**
 * @generated from message api.vtubers.v1.GetVtuberGalleryByIdRequest
 */
export type GetVtuberGalleryByIdRequest = Message<"api.vtubers.v1.GetVtuberGalleryByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.vtubers.v1.GetVtuberGalleryByIdRequest.
 * Use `create(GetVtuberGalleryByIdRequestSchema)` to create a new message.
 */
export const GetVtuberGalleryByIdRequestSchema: GenMessage<GetVtuberGalleryByIdRequest> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubergallery, 4);

/**
 * @generated from message api.vtubers.v1.GetVtuberGalleryByIdResponse
 */
export type GetVtuberGalleryByIdResponse = Message<"api.vtubers.v1.GetVtuberGalleryByIdResponse"> & {
  /**
   * @generated from field: api.vtubers.v1.VtuberGallery data = 1;
   */
  data?: VtuberGallery;
};

/**
 * Describes the message api.vtubers.v1.GetVtuberGalleryByIdResponse.
 * Use `create(GetVtuberGalleryByIdResponseSchema)` to create a new message.
 */
export const GetVtuberGalleryByIdResponseSchema: GenMessage<GetVtuberGalleryByIdResponse> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubergallery, 5);

/**
 * @generated from message api.vtubers.v1.GetVtuberGalleriesRequest
 */
export type GetVtuberGalleriesRequest = Message<"api.vtubers.v1.GetVtuberGalleriesRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 vtuber_id = 1;
   */
  vtuberId: bigint;

  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
   */
  pagination?: PaginationRequest;
};

/**
 * Describes the message api.vtubers.v1.GetVtuberGalleriesRequest.
 * Use `create(GetVtuberGalleriesRequestSchema)` to create a new message.
 */
export const GetVtuberGalleriesRequestSchema: GenMessage<GetVtuberGalleriesRequest> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubergallery, 6);

/**
 * @generated from message api.vtubers.v1.GetVtuberGalleriesResponse
 */
export type GetVtuberGalleriesResponse = Message<"api.vtubers.v1.GetVtuberGalleriesResponse"> & {
  /**
   * @generated from field: repeated api.vtubers.v1.VtuberGallery data = 1;
   */
  data: VtuberGallery[];

  /**
   * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
   */
  paginationDetails?: PaginationDetails;
};

/**
 * Describes the message api.vtubers.v1.GetVtuberGalleriesResponse.
 * Use `create(GetVtuberGalleriesResponseSchema)` to create a new message.
 */
export const GetVtuberGalleriesResponseSchema: GenMessage<GetVtuberGalleriesResponse> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubergallery, 7);

/**
 * @generated from message api.vtubers.v1.UpdateVtuberGalleryByIdRequest
 */
export type UpdateVtuberGalleryByIdRequest = Message<"api.vtubers.v1.UpdateVtuberGalleryByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string media = 2;
   */
  media: string;

  /**
   * @gotag: validate:"required,oneof=picture video"
   *
   * @generated from field: string media_type = 3;
   */
  mediaType: string;

  /**
   * @generated from field: optional string description = 4;
   */
  description?: string;
};

/**
 * Describes the message api.vtubers.v1.UpdateVtuberGalleryByIdRequest.
 * Use `create(UpdateVtuberGalleryByIdRequestSchema)` to create a new message.
 */
export const UpdateVtuberGalleryByIdRequestSchema: GenMessage<UpdateVtuberGalleryByIdRequest> = /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubergallery, 8);

/**
 * @generated from service api.vtubers.v1.VtuberGalleryService
 */
export const VtuberGalleryService: GenService<{
  /**
   * @generated from rpc api.vtubers.v1.VtuberGalleryService.AddVtuberGallery
   */
  addVtuberGallery: {
    methodKind: "unary";
    input: typeof AddVtuberGalleryRequestSchema;
    output: typeof AddVtuberGalleryResponseSchema;
  },
  /**
   * @generated from rpc api.vtubers.v1.VtuberGalleryService.GetVtuberGalleryById
   */
  getVtuberGalleryById: {
    methodKind: "unary";
    input: typeof GetVtuberGalleryByIdRequestSchema;
    output: typeof GetVtuberGalleryByIdResponseSchema;
  },
  /**
   * @generated from rpc api.vtubers.v1.VtuberGalleryService.GetVtuberGalleries
   */
  getVtuberGalleries: {
    methodKind: "unary";
    input: typeof GetVtuberGalleriesRequestSchema;
    output: typeof GetVtuberGalleriesResponseSchema;
  },
  /**
   * @generated from rpc api.vtubers.v1.VtuberGalleryService.DeleteVtuberGalleryById
   */
  deleteVtuberGalleryById: {
    methodKind: "unary";
    input: typeof DeleteVtuberGalleryByIdRequestSchema;
    output: typeof GenericResponseSchema;
  },
  /**
   * @generated from rpc api.vtubers.v1.VtuberGalleryService.UpdateVtuberGalleryById
   */
  updateVtuberGalleryById: {
    methodKind: "unary";
    input: typeof UpdateVtuberGalleryByIdRequestSchema;
    output: typeof GenericResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_vtubers_v1_vtubergallery, 0);

