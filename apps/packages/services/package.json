{"name": "@vtuber/services", "version": "1.0.0", "description": "", "type": "module", "private": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@bufbuild/protobuf": "^2.5.2", "@connectrpc/connect": "^2.0.2", "@connectrpc/connect-web": "^2.0.2", "async-mutex": "^0.5.0", "react-hook-form": "^7.58.1", "sonner": "^2.0.5"}, "exports": {"./client": "./src/client.ts", "./error_details": "./src/gen/google/rpc/error_details_pb.ts", "./code": "./src/gen/google/rpc/code_pb.ts", "./billing": "./src/domains/billing.ts", "./campaigns": "./src/domains/campaigns.ts", "./cms": "./src/domains/cms.ts", "./content": "./src/domains/content.ts", "./events": "./src/domains/events.ts", "./notifications": "./src/domains/notifications.ts", "./shared": "./src/domains/shared.ts", "./taxonomy": "./src/domains/taxonomy.ts", "./users": "./src/domains/users.ts", "./vtubers": "./src/domains/vtubers.ts", "./socials": "./src/domains/socials.ts", "./userdeliveryaddress": "./src/domains/userdeliveryaddress.ts"}, "devDependencies": {"@types/node": "^24.0.3"}}