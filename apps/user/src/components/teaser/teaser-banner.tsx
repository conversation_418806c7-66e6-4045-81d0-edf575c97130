import { Button } from "@vtuber/ui/components/button";
import { Image } from "@vtuber/ui/components/image";
import { cn } from "@vtuber/ui/lib/utils";
import { DottedGroup } from "./dotted-group";
import { RectanglesGroup } from "./rectangles-group";

export const TeaserBanner = ({ className }: { className?: string }) => {
  return (
    <div
      className={cn(
        "bg-gradient-3 items-center justify-center flex md:flex-row flex-col md:px-0 px-5 md:py-20 py-72 gap-24 relative -translate-y-20",
        className,
      )}>
      <div className="absolute top-0 right-0 ">
        <RectanglesGroup />
      </div>
      <div className="absolute left-0 bottom-0">
        <DottedGroup />
      </div>
      <section className="space-y-14">
        <p
          className="lg:w-[520px] text-[40px] font-bold"
          style={{
            background:
              "linear-gradient(-79deg, #85F3FB 13%, #A470CC 81%, #9376CD)",
            backgroundClip: "text",
            WebkitTextFillColor: "transparent",
            WebkitBackgroundClip: "text",
          }}>
          推しと盛り上がり 推しとの特別な瞬間を過ごす
        </p>
        <Button className="md:w-[414px] w-full bg-gradient-2 text-white h-[60px] font-bold">
          公式サイトを見る
        </Button>
      </section>
      <section>
        <Image
          src="https://cdn.v-sai.com/assets/teaser_banner_image.png"
          alt="Teaser Hero Image"
          className="h-[409px]"
        />
      </section>
    </div>
  );
};
