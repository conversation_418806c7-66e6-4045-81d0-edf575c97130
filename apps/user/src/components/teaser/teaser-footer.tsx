import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Container } from "@vtuber/ui/components/container";
import { Logo } from "@vtuber/ui/components/logo";

export const TeaserFooter = () => {
  const { getText } = useLanguage();
  return (
    <Container className="pb-10 space-y-10">
      <Link
        to="/"
        className="flex items-center flex-col">
        <Logo />
        <h6
          className="text-[37px] font-bold text-sub"
          style={{
            textShadow: "1px 2px 0px #B1B1B1",
            filter: "drop-shadow(0px 0px 3px #B1B1B1)",
          }}>
          -V祭-
        </h6>
      </Link>
      <div className="flex items-center justify-center gap-6 text-gray01 text-sm pt-4 md:flex-nowrap flex-wrap">
        <Link
          className="hover:underline"
          to="/terms">
          {getText("terms_of_use")}
        </Link>
        <Link
          className="hover:underline"
          to="/transaction-act">
          {getText("transaction_act")}
        </Link>
        <Link
          className="hover:underline"
          to="/privacy">
          {getText("privacy_policy")}
        </Link>
        <Link
          className="hover:underline"
          to="/operating-company">
          {getText("operating_company")}
        </Link>
      </div>
      <small className="text-xs text-font text-center block">
        ©{new Date().getFullYear()} Vsai. All Rights Reserved.
      </small>
    </Container>
  );
};
