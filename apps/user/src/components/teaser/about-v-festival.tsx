import { Avatar } from "@vtuber/ui/components/avatar";
import { But<PERSON> } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { CloseIcon } from "@vtuber/ui/components/icons/close-icon";
import { ExchangeIcon } from "@vtuber/ui/components/icons/exchange-icon";
import { Logo } from "@vtuber/ui/components/logo";
import { TeaserTitle } from "./teaser-title";

export const AboutVfestival = () => {
  return (
    <Container className="space-y-28">
      <TeaserTitle
        title="What is the V Festival?"
        subTitle="-V祭- とは？"
      />
      <section className="bg-sub rounded-[16px] md:p-20 p-5 relative pt-24">
        <div
          className="bg-gradient-3 px-12 py-[15px] text-center text-font font-bold lg:text-[28px] text-[22px] md:w-auto w-[90%] absolute left-1/2 -translate-x-1/2 md:text-nowrap -top-9"
          style={{
            filter: "drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25))",
          }}>
          VTuberとファンが一緒に夢を叶える場所
          <div className="bg-[#31324F] size-8 absolute -bottom-4 left-1/2 -translate-x-1/2 rotate-[45deg]" />
        </div>
        <div className="text-center text-font sm:text-base text-sm">
          <p>『V祭』は、VTuberが活動資金を集めるだけでなく、</p>
          <p>ファンとの双方向コミュニケーションや参加型イベントを通じて、</p>
          <p>
            認知拡大やブランド強化を実現するクラウドファンディングプラットフォームです
          </p>
        </div>
        <div className="bg-gradient-4 p-[1px] rounded-[6px] mt-28 relative">
          <div className="absolute bg-gradient-4 rounded-[8px] p-0.5 z-20 left-1/2 -translate-x-1/2 -top-14 md:w-auto w-[90%]">
            <div className="bg-sub sm:px-12 sm:py-[26px] px-5 py-5 rounded-[6px] flex items-center justify-center gap-x-6">
              <Logo />
              <h6
                className="text-[37px] font-bold text-sub"
                style={{
                  textShadow: "1px 2px 0px #B1B1B1",
                  filter: "drop-shadow(0px 0px 3px #B1B1B1)",
                }}>
                -V祭-
              </h6>
            </div>
          </div>
          <div className="bg-sub p-8 rounded-[6px] space-y-12 pt-32">
            <div className="flex flex-col items-center gap-y-[37px]">
              <div className="flex gap-[15px] items-stretch">
                <div className="bg-[#85F3FB] w-[2px] h-[33px] rotate-[-20deg]" />
                <p className="font-bold md:text-2xl text-[17px] text-transparent bg-clip-text bg-[#85F3FB] text-center">
                  業界初！ 一体型支援プラットフォーム
                </p>
                <div className="bg-[#85F3FB] w-[2px] h-[33px] rotate-[20deg]" />
              </div>
              <div className="flex items-center md:flex-row flex-col md:gap-3 gap-6">
                <Button
                  className="rounded-full lg:w-[405px] lg:flex-auto md:flex-1 border-[#52C7DA] text-[#52C7DA] h-[64px] md:text-lg text-sm font-bold w-full"
                  variant={"outline"}>
                  クラウドファンディング（資金調達）
                </Button>
                <CloseIcon variant="gradient" />
                <Button
                  className="rounded-full lg:w-[405px] lg:flex-auto md:flex-1 border-purple01 text-purple01 h-[64px] md:text-lg text-sm w-full font-bold"
                  variant={"outline"}>
                  ファン参加型イベント
                </Button>
              </div>
              <h6 className="text-center text-font">
                業界初の、クラウドファンディング（資金調達）×ファン参加型イベントの一体型支援が可能になり
                <br />
                活動の後押しになる、新たな嬉しい場所
              </h6>
            </div>
            <div className="flex md:items-stretch items-center justify-between">
              <section className="flex flex-col items-center gap-y-6">
                <div className="sm:size-[183px] size-[76px] relative">
                  <Avatar
                    src={"https://cdn.v-sai.com/assets/vtuber_image.png"}
                    className="size-full"
                    style={{
                      filter: "drop-shadow(0px 0px 9px #FFFFFF)",
                    }}
                  />
                  <div className="bg-gradient-3 py-2 px-4 rounded-[8px] text-font font-bold sm:text-xl text-[10px] text-center absolute left-1/2 -translate-x-1/2 -bottom-4">
                    Vtuber
                  </div>
                </div>
                <div className="md:block hidden">
                  <h6 className="text-center pt-6 text-xl font-bold text-font underline leading-loose">
                    クラウドファンディングを実施し、 <br />{" "}
                    ファンの支援で夢を現実に。
                  </h6>
                  <p className="text-font text-center">
                    新しいモデル制作やライブ映像など、ファンの支援
                    <br />
                    で夢を実現させるクラウドファンディングの実施。
                  </p>
                </div>
              </section>
              <section className="md:block flex flex-col items-center gap-y-10">
                <div className="border border-[#27595D] p-2 rounded-[5px] text-[#85F3FB] w-fit md:text-balance text-xs">
                  モデル制作や
                  <br />
                  ライブ映像などを実現
                </div>
                <ExchangeIcon className="sm:!w-[282px] w-[117px]" />
                <div className="border border-[#4A2765] p-2 rounded-[5px] text-purple01 w-fit sm:ml-auto text-right md:text-balance text-xs">
                  推しの新しい衣装や
                  <br />
                  ライブ実現を支援
                </div>
              </section>
              <section className="flex flex-col items-center">
                <div className="sm:size-[183px] size-[76px] relative">
                  <Avatar
                    src={"https://cdn.v-sai.com/assets/fan_image.png"}
                    className="size-full"
                    style={{
                      filter: "drop-shadow(0px 0px 9px #FFFFFF)",
                    }}
                  />
                  <div className="bg-gradient-3 py-2 px-4 rounded-[8px] text-font font-bold sm:text-xl text-[10px] text-center absolute text-nowrap left-1/2 -translate-x-1/2 -bottom-4">
                    ファン
                  </div>
                </div>
                <div className="md:block hidden">
                  <h6 className="text-center pt-6 text-xl font-bold text-font underline leading-loose">
                    クラウドファンディングで支援し、 <br />{" "}
                    VTuberの夢の実現に立ち会う。
                  </h6>
                  <p className="text-font text-center">
                    あなたの支援が、推しの新しい衣装やライブ実現の
                    <br />
                    力になる。VTuberの成長そのものを後押しします。
                  </p>
                </div>
              </section>
            </div>
            <div className="md:hidden flex flex-col items-center gap-y-6">
              <section className="space-y-6">
                <div className="text-lg font-bold px-4 py-2 border-blue02 border w-max mx-auto">
                  Vtuber
                </div>
                <div className="space-y-5">
                  <h6 className="underline text-center leading-relaxed font-bold">
                    クラウドファンディングを実施し、 ファンの支援で夢を現実に。
                  </h6>
                  <p className="text-sm font-medium">
                    新しいモデル制作やライブ映像など、ファンの支援で夢を実現させるクラウドファンディングの実施。
                  </p>
                </div>
              </section>
              <section className="space-y-6">
                <div className="text-lg font-bold px-4 py-2 border-purple01 border w-max mx-auto">
                  ファン
                </div>
                <div className="space-y-5">
                  <h6 className="underline text-center leading-relaxed font-bold">
                    クラウドファンディングで支援し、
                    VTuberの夢の実現に立ち会う。
                  </h6>
                  <p className="text-sm font-medium">
                    あなたの支援が、推しの新しい衣装やライブ実現の力になる。VTuberの成長そのものを後押しします。
                  </p>
                </div>
              </section>
            </div>
          </div>
        </div>
      </section>
    </Container>
  );
};
