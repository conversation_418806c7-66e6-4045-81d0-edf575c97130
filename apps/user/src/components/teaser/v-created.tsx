import { Container } from "@vtuber/ui/components/container";
import { TeaserTitle } from "./teaser-title";

export const VCreated = () => {
  return (
    <div className="space-y-6 overflow-hidden">
      <TeaserTitle
        title="Why was it created?"
        subTitle="V祭がなぜ生まれたのか？"
      />
      <div className="md:space-y-12 space-y-16">
        <Container>
          <div className="bg-[#29273B] md:p-12 p-8 space-y-8">
            <div
              className="bg-gradient-3"
              style={{
                filter: "drop-shadow(0px 4px 4px rgba(0,0,0,25%))",
              }}>
              <p className="text-center md:w-[671px] mx-auto text-font md:text-[28px] text-xl font-bold md:px-12 px-5 md:py-[15px] py-5">
                バーチャルで輝く才能あるクリエイターたちの夢を、
                一人でも多く叶えたい
              </p>
            </div>
            <div className="text-font md:text-lg text-base font-medium leading-relaxed">
              <p>
                2023年以降、VTuber市場は急速に成長を遂げており、企業所属のVTuberのみならず、事務所には所属せずに個人でVTuber活動を行う「個人勢」の存在感も増しています。一方で個人での活動は、活動資金の確保や新たなファンとの接点づくり、持続的なモチベーションの維持といった点で、多くの課題を抱えています。特に、3DモデルやMV制作といった大きなコストがかかる取り組みは、熱量があっても実行に移しづらい状況にあります。
              </p>
              <p>
                当社は2D/3DCG制作会社として、これまで●●人以上のVTuberのクリエイティブサポートを手がける中で、彼らの切実な声を数多く聞いてまいりました。その想いに応えるために立ち上げたのが、VTuberの夢の実現を支援する『V祭』です。
              </p>
            </div>
          </div>
        </Container>
        <section className="space-y-6">
          <Container className="grid grid-cols-12 items-center md:gap-y-0 gap-y-14">
            <section className="md:col-span-6 col-span-12 space-y-6 md:order-1 order-2">
              <h6 className="font-bold text-[28px] text-[#85F3FB]">
                VTuber支援の新たなスタンダードへ
              </h6>
              <div className="text-font text-lg font-medium">
                <p>
                  クラウドファンディングは、これまで“資金を集める手段”として活用されることが多くありました。しかし『V祭』が目指すのは、それを“ファンと一緒につくる場所”へと進化させることです。
                </p>
                <p>
                  VTuberのアイデアや挑戦が、ファンの応援によって形になっていく
                  ——そのプロセスそのものを価値ある体験に変える仕組みを提供します。
                </p>
              </div>
            </section>
            <section className="md:col-span-4 col-span-12 md:order-2 order-1">
              <div className="md:w-[476px] h-[315px] relative">
                <div className="absolute flex flex-col z-20 gap-y-3 items-start bottom-6 md:-right-8 -right-4">
                  <div className="h-[1px] w-[50px] bg-gradient-4" />
                  <div className="h-[1px] w-[60px] bg-gradient-4 ml-1" />
                </div>
                <div className="absolute flex flex-col z-20 gap-y-3 top-6 md:-left-8 -left-4">
                  <div className="h-[1px] w-[60px] bg-gradient-4 ml-4" />
                  <div className="h-[1px] w-[50px] bg-gradient-4" />
                </div>
                <div
                  className="relative overflow-hidden size-full bg-[url('https://cdn.v-sai.com/assets/v_created_at_bg.jpg')] bg-cover bg-no-repeat"
                  style={{
                    clipPath: "polygon(0 0, 100% 0, 100% 100%, 15% 100%)",
                  }}>
                  <div className="absolute inset-0 bg-gradient-to-r from-[#56477D] to-[#4C8B95] opacity-5" />
                  <div className="absolute inset-0 bg-gradient-to-r from-[#56477D] to-[#4C8B95] opacity-10" />
                </div>
              </div>
            </section>
          </Container>
          <div className="relative md:translate-y-0 -translate-y-20 overflow-hidden h-[244px]">
            <div
              className="bg-gradient-to-b from-[#19172C] to-[#2C294C] h-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 md:w-full w-[1280px]"
              style={{
                clipPath: "polygon(0 0, 50% 37%, 100% 0)",
                WebkitClipPath: "polygon(0 0, 50% 100%, 100% 0)",
              }}
            />
          </div>
          <Container className="md:pt-16">
            <div className="p-[1px] rounded-[8px] bg-gradient-4">
              <div className="md:px-[107px] px-5 md:py-16 py-5 rounded-[8px] space-y-4 bg-sub">
                <h6 className="text-[#85F3FB] font-bold md:text-[32px] text-[23px]">
                  ファンとともに世界観を築く、参加型クラウドファンディング
                </h6>
                <div className="text-font">
                  <p>
                    プロジェクトに“ストーリー性”や“一体感”が加わる新しい仕組み
                  </p>
                  <p>
                    V祭では、単なる資金調達にとどまらず、イベントや参加型企画を通じて、VTuberとファンが一緒にストーリーや世界観を共有・発展させていくことが可能です。応援が“体験”となり、ファンとの関係性がより深まります。
                  </p>
                </div>
              </div>
            </div>
          </Container>
        </section>
      </div>
    </div>
  );
};
