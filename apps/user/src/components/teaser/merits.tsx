import { Container } from "@vtuber/ui/components/container";
import { BadgeCheck } from "lucide-react";
import { TeaserTitle } from "./teaser-title";

export const Merits = () => {
  return (
    <div className="space-y-12 relative">
      <div
        className="bg-[#207DF8] h-[1px] md:w-[816px] w-[422px] rotate-[-30deg] absolute left-[-140px] md:top-24 -z-10"
        style={{
          filter: "drop-shadow(0px 0px 9px #fff)",
          msFilter: "drop-shadow(0px 0px 9px #fff)",
          WebkitFilter: "drop-shadow(0px 0px 9px #fff)",
        }}
      />
      <Container>
        <TeaserTitle
          title="Merit"
          subTitle="V祭のクラウドファンディングに"
          caption="参加するメリット"
          rightIconClassName="translate-y-28"
        />
      </Container>
      <div className="grid md:grid-cols-2 grid-cols-1 items-center md:gap-48 gap-12">
        <section className="min-h-[331px] flex flex-col justify-center md:p-3 p-12 md:mt-32 relative md:order-1 order-2">
          <div
            className="w-[125%] md:block hidden -z-10 absolute bg-[url('https://cdn.v-sai.com/assets/vtuber_merits_bg.jpg')] bg-cover bg-no-repeat h-full top-0 left-0"
            style={{
              clipPath: "polygon(0 0, 80% 0%, 100% 100%, 0% 100%)",
              WebkitClipPath: "polygon(0 0, 80% 0%, 100% 100%, 0% 100%)",
              filter: "drop-shadow(0px 0px 70px #0E0B23)",
              msFilter: "drop-shadow(0px 0px 70px #0E0B23)",
              WebkitFilter: "drop-shadow(0px 0px 70px #0E0B23)",
              backgroundPosition: "center right",
            }}>
            <div className="absolute inset-0 bg-black/70" />
          </div>
          <div
            className="md:hidden block -z-10 absolute bg-[url('https://cdn.v-sai.com/assets/vtuber_merits_bg.jpg')] bg-cover bg-no-repeat size-full top-0 left-0"
            style={{
              clipPath: "polygon(94% 0, 100% 50%, 100% 100%, 0 100%, 0 0)",
              WebkitClipPath:
                "polygon(94% 0, 100% 50%, 100% 100%, 0 100%, 0 0)",
              filter: "drop-shadow(0px 0px 70px #0E0B23)",
              msFilter: "drop-shadow(0px 0px 70px #0E0B23)",
              WebkitFilter: "drop-shadow(0px 0px 70px #0E0B23)",
              backgroundPosition: "center right",
            }}>
            <div className="absolute inset-0 bg-black/70" />
          </div>

          <div className="md:max-w-[420px] ml-auto space-y-[22px]">
            <div className="-space-y-3">
              <h6 className="font-montserrat text-[32px] font-medium">
                VTuber
              </h6>
              <div className="bg-white w-max -skew-x-[6deg]">
                <h6 className=" text-transparent bg-clip-text bg-gradient-3 text-[29px] font-extrabold">
                  VTuberのメリット
                </h6>
              </div>
            </div>
            <div className="space-y-3 text-font ">
              <div className="flex items-center gap-2">
                <BadgeCheck className="!size-[21px]" />
                <p className="font-medium">
                  新たな衣装やライブ実現に必要な資金を集められる
                </p>
              </div>
              <div className="w-full h-[1px] border border-dotted border-white" />
              <div className="flex items-center gap-2">
                <BadgeCheck className="!size-[21px]" />
                <p className="font-medium">
                  想いを受け取りファンとの関係性をさらに深められる
                </p>
              </div>
              <div className="w-full h-[1px] border border-dotted border-white" />
              <div className="flex items-center gap-2">
                <BadgeCheck className="!size-[21px]" />
                <p className="font-medium">
                  話題性を生み、新しいファンとの出会いにつながる
                </p>
              </div>
            </div>
          </div>
        </section>
        <section className="min-h-[331px] flex flex-col justify-center md:p-3 p-12 relative md:order-2 order-1">
          <div
            className="w-[125%] md:block hidden -z-10 absolute bg-[url('https://cdn.v-sai.com/assets/fans_merit_bg.jpg')] bg-cover bg-no-repeat h-full top-0 right-0"
            style={{
              clipPath: "polygon(0 0, 100% 0%, 100% 100%, 20% 100%)",
              WebkitClipPath: "polygon(0 0, 100% 0%, 100% 100%, 20% 100%)",
              filter: "drop-shadow(0px 0px 70px #0E0B23)",
              msFilter: "drop-shadow(0px 0px 70px #0E0B23)",
              WebkitFilter: "drop-shadow(0px 0px 70px #0E0B23)",
              backgroundPosition: "center right",
            }}>
            <div className="absolute inset-0 bg-black/70" />
          </div>
          <div
            className="md:hidden block -z-10 absolute bg-[url('https://cdn.v-sai.com/assets/fans_merit_bg.jpg')] bg-cover bg-no-repeat size-full top-0 right-0"
            style={{
              clipPath: "polygon(0 0, 100% 0, 100% 100%, 6% 100%, 0 50%)",
              WebkitClipPath: "polygon(0 0, 100% 0, 100% 100%, 6% 100%, 0 50%)",
              filter: "drop-shadow(0px 0px 70px #0E0B23)",
              msFilter: "drop-shadow(0px 0px 70px #0E0B23)",
              WebkitFilter: "drop-shadow(0px 0px 70px #0E0B23)",
              backgroundPosition: "center right",
            }}>
            <div className="absolute inset-0 bg-black/70" />
          </div>
          <div className="md:max-w-[420px] md:mr-auto space-y-[22px]">
            <div className="-space-y-3">
              <h6 className="font-montserrat text-[32px] font-medium">
                FAN USER
              </h6>
              <div className="bg-white w-max -skew-x-[6deg]">
                <h6 className=" text-transparent bg-clip-text bg-gradient-3 text-[29px] font-extrabold">
                  ファンのメリット
                </h6>
              </div>
            </div>
            <div className="space-y-3 text-font md:max-w-[420px]">
              <div className="flex items-center gap-2">
                <BadgeCheck className="!size-[21px]" />
                <p className="font-medium">
                  推しの夢を“手が届く形”で応援し、VTuberの成長に関われる
                </p>
              </div>
              <div className="w-full h-[1px] border border-dotted border-white" />
              <div className="flex items-center gap-2">
                <BadgeCheck className="!size-[21px]" />
                <p className="font-medium">
                  支援の使い道が明確で、信頼して応援できる
                </p>
              </div>
              <div className="w-full h-[1px] border border-dotted border-white" />
              <div className="flex items-center gap-2">
                <BadgeCheck className="!size-[21px]" />
                <p className="font-medium">
                  ここでしか手に入らない、リターンが得られる
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};
