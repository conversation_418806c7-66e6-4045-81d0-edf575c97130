import { Link, useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { userDeliveryAddressClient } from "@vtuber/services/client";
import { UserDeliveryAddress } from "@vtuber/services/userdeliveryaddress";
import { Button, buttonVariants } from "@vtuber/ui/components/button";
import { DeleteDialog } from "@vtuber/ui/components/DeleteDialog";
import { useScrollInToView } from "@vtuber/ui/hooks/use-scroll-intoview";
import { cn } from "@vtuber/ui/lib/utils";
import { Edit, MapPin, Phone, Plus, Trash } from "lucide-react";
import { toast } from "sonner";

interface Props {
  activeSection: string | null;
  triggerTimestamp: number;
  address?: UserDeliveryAddress;
}

export const AddressSection = ({
  activeSection,
  triggerTimestamp,
  address,
}: Props) => {
  const router = useRouter();
  const { getText } = useLanguage();
  const { ref } = useScrollInToView({
    trigger: activeSection === "address_info" ? triggerTimestamp : undefined,
    withHeader: true,
    headerHeight: 100,
  });
  const formatAddress = () => {
    if (!address) return "";
    const parts = [
      address.addressLine1,
      address.addressLine2,
      address.city,
      address.prefecture,
      address.postalCode,
    ].filter(Boolean);

    return parts.join(", ");
  };
  return (
    <section
      ref={ref}
      style={{
        filter: "drop-shadow(0px 0px 12px rgba(0,0,0,0.7))",
      }}
      className="grid gap-y-3 bg-sub py-14 md:px-14 px-8 rounded-[24px]">
      <h6 className="text-font text-2xl font-medium">
        {getText("delivery_information")}
      </h6>
      <div className="border border-font rounded-[3px] py-3 px-4">
        <div className="h-full w-full bg-[#555367] rounded-[3px] border border-dashed border-font text-sub text-center py-[9px] px-4">
          {getText(
            address
              ? "manage_your_delivery_address"
              : "delivery_info_not_saved",
          )}
        </div>
      </div>
      <p className="text-font text-xs">
        {getText("update_reward_address_instruction")}
      </p>
      {address && (
        <div className="hover:bg-white/10 relative flex w-full items-center gap-3 rounded-md border border-gray-700 hover:border-font p-4 shadow-xs bg-background">
          <MapPin className="h-5 w-5 text-muted-foreground flex-shrink-0" />

          <div className="flex-1 min-w-0">
            <p className="text-lg font-medium truncate">{address.recipient}</p>
            <p className="text-muted-foreground text-sm truncate">
              {formatAddress()}
            </p>
            <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
              <Phone className="h-3 w-3" />
              <span>{address.phoneNumber}</span>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Button
              asChild
              size="icon"
              variant="accent"
              className="flex-shrink-0">
              <Link to="/delivery-address">
                <Edit className="size-4" />
                <span className="sr-only">Edit delivery address</span>
              </Link>
            </Button>
            <DeleteDialog
              asChild
              name="Delivery Address"
              onDelete={async () => {
                const [res, err] =
                  await userDeliveryAddressClient.deleteUserDeliveryAddress({
                    id: address.id,
                  });
                if (res) {
                  toast.success(res.message);
                  router.invalidate();
                  return;
                }
                toast.error(err.message);
              }}>
              <Button
                size={"icon"}
                variant={"muted-destructive"}>
                <Trash />
              </Button>
            </DeleteDialog>
          </div>
        </div>
      )}
      {!address && (
        <Link
          to="/delivery-address"
          className={cn(
            buttonVariants({
              size: "lg",
              variant: "blue-outline",
            }),
            "w-max rounded-[3px]",
          )}>
          <Plus className="mr-2" />
          {getText("register_new")}
        </Link>
      )}
    </section>
  );
};
