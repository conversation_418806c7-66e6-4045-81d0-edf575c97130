import { useMutation } from "@connectrpc/connect-query";
import { useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import {
  AddBillingInfoRequest,
  BillInfoService,
  BillingInfo,
} from "@vtuber/services/billing";
import { handleConnectError, OmitTypeName } from "@vtuber/services/client";
import { Button } from "@vtuber/ui/components/button";
import { Form } from "@vtuber/ui/components/form";
import { CardNumberInput } from "@vtuber/ui/components/form-inputs/card-number-input";
import { CVCInput } from "@vtuber/ui/components/form-inputs/cvc-input";
import { ExpiryInput } from "@vtuber/ui/components/form-inputs/expiry-input";
import { SelectInput } from "@vtuber/ui/components/form-inputs/select-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { NavigationBlocker } from "@vtuber/ui/components/navigation-blocker";
import { useScrollInToView } from "@vtuber/ui/hooks/use-scroll-intoview";
import { prefectures } from "@vtuber/ui/lib/constants";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

type Props = {
  creditCard?: BillingInfo;
  id?: number;
  goBack?: () => void;
};

export const CreditCardForm = ({ creditCard, id, goBack }: Props) => {
  const { getText } = useLanguage();
  const { session } = useAuth();
  const queryClient = useQueryClient();

  const form = useForm<OmitTypeName<AddBillingInfoRequest>>({
    defaultValues: {
      fullName: creditCard?.fullName ?? session?.user?.fullName,
      address1: creditCard?.address1 || "",
      address2: creditCard?.address2,
      city: creditCard?.city,
      state: creditCard?.state,
      country: "Japan",
      postalCode: creditCard?.postalCode,
      companyName: creditCard?.companyName,
      vatNumber: creditCard?.vatNumber,
      cardNo: creditCard?.cardNo || "",
      cardExpiry: creditCard?.cardExpiry || "",
      cardCvc: "",
    },
  });

  const { ref } = useScrollInToView({
    trigger: !!id,
    withHeader: true,
    headerHeight: 100,
  });

  const { mutate: addCardMutation, isPending: addingCard } = useMutation(
    BillInfoService.method.addBillingInfo,
    {
      onSuccess: () => {
        form.reset();
        queryClient.invalidateQueries({
          queryKey: ["credit_card"],
        });
        goBack?.();

        toast.success(getText("credit_card_addition_success_message"));
      },
      onError: (err) => {
        handleConnectError(err, form);
        toast.error(err.message);
      },
    },
  );

  const { mutate: updateCardMutation, isPending: updatingCard } = useMutation(
    BillInfoService.method.updateBillingInfo,
    {
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries({
          queryKey: ["credit_card"],
        });
        goBack?.();
        toast.success(data.message);
        form.reset(variables);
      },
      onError: (err) => {
        handleConnectError(err, form);
        toast.error(err.message);
      },
    },
  );

  const onSubmit = form.handleSubmit((data) => {
    if (creditCard) {
      updateCardMutation({
        ...data,
        id: creditCard.id,
        postalCode: String(data.postalCode),
      });
      return;
    }

    addCardMutation({
      ...data,
      postalCode: String(data.postalCode),
    });
  });

  return (
    <div ref={ref}>
      <NavigationBlocker condition={addingCard || updatingCard}>
        <p>{getText("your_card_is_being_processed_please_wait")}</p>
      </NavigationBlocker>

      <Form {...form}>
        <form
          onSubmit={onSubmit}
          className="space-y-10">
          <h3 className="text-font font-medium">
            {getText("credit_card_information")}
          </h3>
          <div className="space-y-8">
            <TextInput
              control={form.control}
              name="fullName"
              wrapperClassName="space-y-3"
              className="h-[48px] rounded-xl"
              label={getText("card_holder_name")}
              placeholder={getText("your_name")}
            />
            <div className="grid sm:grid-cols-2 grid-cols-1 items-center sm:gap-x-4 gap-y-8">
              <TextInput
                control={form.control}
                name="postalCode"
                wrapperClassName="space-y-3 w-full"
                label={getText("postal_code")}
                type="number"
                inputMode="numeric"
                className="h-[48px] rounded-xl"
              />
              <SelectInput
                options={prefectures}
                control={form.control}
                className="h-[48px] rounded-xl"
                name="state"
                label={getText("prefectures")}
                placeholder="Select Prefecture"
              />
              <TextInput
                control={form.control}
                name="city"
                wrapperClassName="space-y-3 w-full"
                label={getText("city")}
                className="h-[48px] rounded-xl"
              />
              <TextInput
                control={form.control}
                name="address1"
                wrapperClassName="space-y-3 w-full"
                label={getText("address_1")}
                className="h-[48px] rounded-xl"
              />
              <TextInput
                control={form.control}
                name="address2"
                label={`${getText("address_2")} (${getText("optional")})`}
                wrapperClassName="space-y-3 w-full"
                className="h-[48px] rounded-xl"
              />
            </div>
            <CardNumberInput
              name="cardNo"
              disabled={!!creditCard}
              wrapperClassName="space-y-3"
              label={getText("card_number")}
              control={form.control}
              className="h-[48px] rounded-xl"
            />
            <div className="flex sm:flex-row flex-col sm:items-center sm:gap-x-4 sm:gap-y-0 gap-y-8">
              <ExpiryInput
                disabled={!!creditCard}
                control={form.control}
                name="cardExpiry"
                wrapperClassName="space-y-3 w-full"
                label={getText("expiration_date")}
                className="h-[48px] rounded-xl"
              />
              <CVCInput
                disabled={!!creditCard}
                control={form.control}
                name="cardCvc"
                label="CVC/CVV"
                wrapperClassName="space-y-3 w-full"
                className="h-[48px] rounded-xl"
              />
            </div>
            <Button
              loading={addingCard || updatingCard}
              className="rounded-full h-11"
              variant={"muted"}>
              {creditCard ? getText("update") : getText("Add")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
