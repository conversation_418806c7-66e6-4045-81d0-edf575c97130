import { createFile<PERSON>oute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { DictionaryKey } from "@vtuber/language/types";
import { userDeliveryAddressClient } from "@vtuber/services/client";
import { Container } from "@vtuber/ui/components/container";
import { Separator } from "@vtuber/ui/components/separator";
import { cn } from "@vtuber/ui/lib/utils";
import { ChevronRight } from "lucide-react";
import { useState } from "react";
import { LogoutConfirmationModal } from "~/components/layout/logout-confirmation-modal";
import { PageTitle } from "~/components/layout/page-title";
import { AddressSection } from "~/components/profile/address-section";
import { CreditCardSection } from "~/components/profile/credit-card-section";
import { NotificationSettings } from "~/components/profile/notification-settings";
import { PasswordForm } from "~/components/profile/password-form";
import { ProfileForm } from "~/components/profile/profile-form";
import { UserEmailUpdater } from "~/components/profile/user-email-updater";

const sideLinks: { label: DictionaryKey; href: string }[] = [
  { label: "edit_profile", href: "profile" },
  { label: "email_address", href: "email_address" },
  { label: "password", href: "password" },
  { label: "credit_card_information", href: "credit_card" },
  { label: "delivery_information", href: "address_info" },
  // { label: "bank_account_information", href: "bank_account" },
  { label: "notification_settings", href: "notification" },
];

export const Route = createFileRoute("/_protected/setting")({
  component: RouteComponent,
  loader: async () => {
    const [res] = await userDeliveryAddressClient.getCurrentUserDeliveryAddress(
      {},
    );

    return { address: res?.data };
  },
});

function RouteComponent() {
  const { address } = Route.useLoaderData();
  const [scrollTrigger, setScrollTrigger] = useState<{
    section: string | null;
    timestamp: number;
  }>({ section: null, timestamp: 0 });

  const { getText } = useLanguage();

  return (
    <div className="relative">
      <Container className="space-y-16">
        <PageTitle title="account_settings" />
        <div className="grid grid-cols-12 lg:gap-x-10 items-start">
          <section className="lg:block hidden col-span-4 sticky top-20 z-20">
            <nav>
              {sideLinks.map((item, i) => (
                <div key={i}>
                  <button
                    onClick={() =>
                      setScrollTrigger({
                        section: item.href,
                        timestamp: Date.now(),
                      })
                    }
                    className={cn(
                      "w-full flex items-center justify-between py-6 px-2 hover:text-primary capitalize",
                      scrollTrigger.section === item.href
                        ? "text-primary"
                        : "text-font",
                    )}>
                    {getText(item.label)}
                    <ChevronRight className="size-6" />
                  </button>
                  <Separator className="bg-font" />
                </div>
              ))}
              <LogoutConfirmationModal asChild>
                <button className="w-full block">
                  <div className="flex items-center justify-between font-bold py-6 px-2 hover:text-primary text-font">
                    {getText("logout")}
                    <ChevronRight className="size-6" />
                  </div>
                  <Separator className="bg-font" />
                </button>
              </LogoutConfirmationModal>
            </nav>
          </section>
          <section className="col-span-12 lg:col-span-8 flex flex-col gap-y-20 text-font">
            <ProfileForm
              activeSection={scrollTrigger.section}
              triggerTimestamp={scrollTrigger.timestamp}
            />
            <UserEmailUpdater
              activeSection={scrollTrigger.section}
              triggerTimestamp={scrollTrigger.timestamp}
            />
            <PasswordForm
              activeSection={scrollTrigger.section}
              triggerTimestamp={scrollTrigger.timestamp}
            />
            <CreditCardSection
              activeSection={scrollTrigger.section}
              triggerTimestamp={scrollTrigger.timestamp}
            />
            <AddressSection
              address={address}
              activeSection={scrollTrigger.section}
              triggerTimestamp={scrollTrigger.timestamp}
            />
            {/* <BankAccountSection
              activeSection={scrollTrigger.section}
              triggerTimestamp={scrollTrigger.timestamp}
            /> */}
            <NotificationSettings
              activeSection={scrollTrigger.section}
              triggerTimestamp={scrollTrigger.timestamp}
            />
          </section>
        </div>
      </Container>
    </div>
  );
}
