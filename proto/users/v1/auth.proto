syntax = "proto3";

package api.users.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "users/v1/users.proto";
import "vtubers/v1/vtuberprofiles.proto";

option go_package = "github.com/nsp-inc/vtuber/api/users/v1;usersv1";

message SignInWithSocialRequest {
  string provider = 1;
  string code = 2;
}

message SignInWithSocialResponse {
  string access_token = 1;
  string refresh_token = 2;
}

message GetSessionRequest {}

message GetSessionResponse {
  User user = 1;
  api.vtubers.v1.VtuberProfile vtuber = 2;
}

message SignOutRequest {}
message SignOutResponse {
  bool success = 1;
  string message = 2;
}

message SendSignupEmailVerificationCodeRequest {
  string email = 1;
}

message SendSignupEmailVerificationCodeResponse {
  bool success = 1;
  string message = 2;
}

message VerifySignupEmailRequest {
  string email = 1;
  string verification_code = 2;
}

message VerifySignupEmailResponse {
  string token = 1;
}

message SignupWithEmailRequest {
  string full_name = 1;
  string password = 2;
  optional google.protobuf.Timestamp date_of_birth = 3;
  string token = 4;
}

message SignupWithEmailResponse {
  bool success = 1;
  string message = 2;
}

message SignInWithEmailRequest {
  string email = 1;
  string password = 2;
}

message SignInWithEmailResponse {
  string access_token = 1;
  string refresh_token = 2;
}

message SendForgotPasswordEmailRequest {
  string email = 1;
}

message SendForgotPasswordEmailResponse {
  bool success = 1;
  string message = 2;
}

message ResetPasswordRequest {
  string email = 1;
  string verification_code = 2;
  string new_password = 3;
}

message ResetPasswordResponse {
  bool success = 1;
  string message = 2;
}

message ChangePasswordRequest {
  string old_password = 1;
  string new_password = 2;
}

message ChangePasswordResponse {
  bool success = 1;
  string message = 2;
}

message ChangeEmailVerificationRequest {}

message ChangeEmailVerificationResponse {
  bool success = 1;
  string message = 2;
}

message VerifyChangeEmailRequest {
  string verification_code = 1;
}

message VerifyChangeEmailResponse {
  bool success = 1;
  string token = 2;
}

message SendVerifyNewEmailRequest {
  string new_email = 2;
  string token = 3;
}

message SendVerifyNewEmailResponse {
  bool success = 1;
  string message = 2;
}

message VerifyNewEmailWithCodeRequest {
  string verification_code = 1;
  string new_email = 2;
}

message VerifyNewEmailRequest {
  optional VerifyNewEmailWithCodeRequest verify_new_email_with_code = 1;
  optional string token = 2;
}

message VerifyNewEmailResponse {
  bool success = 1;
  string message = 2;
}

message UpdateUserDetailsRequest {
  string full_name = 1;
  optional google.protobuf.Timestamp date_of_birth = 2;
}

message UpdateUserDetailsResponse {
  bool success = 1;
  string message = 2;
}

message UpdateUserImageRequest {
  string image = 1;
}

message UpdateUserImageResponse {
  bool success = 1;
  string message = 2;
}

message ListSessionsRequest {}

message Session {
  string id = 1;
  string user_agent = 2;
  string ip_address = 3;
  string created_at = 4;
  bool isCurrent = 5;
}

message ListSessionsResponse {
  repeated Session sessions = 1;
}

message RevokeSessionsRequest {
  repeated string session_ids = 1;
}

message RevokeSessionsResponse {
  bool success = 1;
  string message = 2;
}

message RevokeOtherSessionsRequest {}

message RevokeOtherSessionsResponse {
  bool success = 1;
  string message = 2;
}

message LinkSocialRequest {
  string provider = 1;
  string code = 2;
}

message LinkSocialResponse {
  bool success = 1;
  string message = 2;
}

message ListAccountsRequest {}

message ListAccountsResponse {
  repeated Account accounts = 1;
}
message Account {
  string id = 1;
  string provider = 2;
  string user_id = 3;
}

message UnlinkAccountRequest {
  string account_id = 1;
}
message UnlinkAccountResponse {
  bool success = 1;
  string message = 2;
}

message RefreshTokenRequest {
  string refresh_token = 1;
}

message RefreshTokenResponse {
  string access_token = 1;
  string refresh_token = 2;
}

service AuthService {
  rpc SignInWithEmail(SignInWithEmailRequest) returns (SignInWithEmailResponse) {}
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse) {}
  rpc SignInWithSocial(SignInWithSocialRequest) returns (SignInWithSocialResponse) {}
  rpc SignOut(SignOutRequest) returns (SignOutResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetSession(GetSessionRequest) returns (GetSessionResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc SendSignupEmailVerificationCode(SendSignupEmailVerificationCodeRequest) returns (SendSignupEmailVerificationCodeResponse) {}
  rpc VerifySignupEmail(VerifySignupEmailRequest) returns (VerifySignupEmailResponse) {}
  rpc SignupWithEmail(SignupWithEmailRequest) returns (SignupWithEmailResponse) {}
  rpc SendForgotPasswordEmail(SendForgotPasswordEmailRequest) returns (SendForgotPasswordEmailResponse) {}
  rpc ResetPassword(ResetPasswordRequest) returns (ResetPasswordResponse) {}
  rpc ChangePassword(ChangePasswordRequest) returns (ChangePasswordResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc ChangeEmailVerification(ChangeEmailVerificationRequest) returns (ChangeEmailVerificationResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc VerifyChangeEmail(VerifyChangeEmailRequest) returns (VerifyChangeEmailResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc SendVerifyNewEmail(SendVerifyNewEmailRequest) returns (SendVerifyNewEmailResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc VerifyNewEmail(VerifyNewEmailRequest) returns (VerifyNewEmailResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc UpdateUserDetails(UpdateUserDetailsRequest) returns (UpdateUserDetailsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc UpdateUserImage(UpdateUserImageRequest) returns (UpdateUserImageResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc ListSessions(ListSessionsRequest) returns (ListSessionsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc RevokeSessions(RevokeSessionsRequest) returns (RevokeSessionsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc RevokeOtherSessions(RevokeOtherSessionsRequest) returns (RevokeOtherSessionsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc LinkSocial(LinkSocialRequest) returns (LinkSocialResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc ListAccounts(ListAccountsRequest) returns (ListAccountsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc UnlinkAccount(UnlinkAccountRequest) returns (UnlinkAccountResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
