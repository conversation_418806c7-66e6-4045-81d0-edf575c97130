// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: notifications/v1/notifications.proto

package notificationsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetNotificationsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNotificationsRequest) Reset() {
	*x = GetNotificationsRequest{}
	mi := &file_notifications_v1_notifications_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationsRequest) ProtoMessage() {}

func (x *GetNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notifications_v1_notifications_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationsRequest.ProtoReflect.Descriptor instead.
func (*GetNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_notifications_v1_notifications_proto_rawDescGZIP(), []int{0}
}

func (x *GetNotificationsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetUserNotificationsResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*Notification        `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetUserNotificationsResponse) Reset() {
	*x = GetUserNotificationsResponse{}
	mi := &file_notifications_v1_notifications_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserNotificationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserNotificationsResponse) ProtoMessage() {}

func (x *GetUserNotificationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notifications_v1_notifications_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserNotificationsResponse.ProtoReflect.Descriptor instead.
func (*GetUserNotificationsResponse) Descriptor() ([]byte, []int) {
	return file_notifications_v1_notifications_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserNotificationsResponse) GetData() []*Notification {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetUserNotificationsResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type Notification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Json          *string                `protobuf:"bytes,4,opt,name=json,proto3,oneof" json:"json,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	DeepLink      *string                `protobuf:"bytes,6,opt,name=deep_link,json=deepLink,proto3,oneof" json:"deep_link,omitempty"`
	Severity      string                 `protobuf:"bytes,7,opt,name=severity,proto3" json:"severity,omitempty"`
	TitleJp       string                 `protobuf:"bytes,8,opt,name=titleJp,proto3" json:"titleJp,omitempty"`
	DescriptionJp string                 `protobuf:"bytes,9,opt,name=descriptionJp,proto3" json:"descriptionJp,omitempty"`
	IsRead        bool                   `protobuf:"varint,10,opt,name=is_read,json=isRead,proto3" json:"is_read,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Notification) Reset() {
	*x = Notification{}
	mi := &file_notifications_v1_notifications_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Notification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notification) ProtoMessage() {}

func (x *Notification) ProtoReflect() protoreflect.Message {
	mi := &file_notifications_v1_notifications_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notification.ProtoReflect.Descriptor instead.
func (*Notification) Descriptor() ([]byte, []int) {
	return file_notifications_v1_notifications_proto_rawDescGZIP(), []int{2}
}

func (x *Notification) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Notification) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Notification) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Notification) GetJson() string {
	if x != nil && x.Json != nil {
		return *x.Json
	}
	return ""
}

func (x *Notification) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Notification) GetDeepLink() string {
	if x != nil && x.DeepLink != nil {
		return *x.DeepLink
	}
	return ""
}

func (x *Notification) GetSeverity() string {
	if x != nil {
		return x.Severity
	}
	return ""
}

func (x *Notification) GetTitleJp() string {
	if x != nil {
		return x.TitleJp
	}
	return ""
}

func (x *Notification) GetDescriptionJp() string {
	if x != nil {
		return x.DescriptionJp
	}
	return ""
}

func (x *Notification) GetIsRead() bool {
	if x != nil {
		return x.IsRead
	}
	return false
}

type MarkNotificationAsReadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkNotificationAsReadRequest) Reset() {
	*x = MarkNotificationAsReadRequest{}
	mi := &file_notifications_v1_notifications_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkNotificationAsReadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkNotificationAsReadRequest) ProtoMessage() {}

func (x *MarkNotificationAsReadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notifications_v1_notifications_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkNotificationAsReadRequest.ProtoReflect.Descriptor instead.
func (*MarkNotificationAsReadRequest) Descriptor() ([]byte, []int) {
	return file_notifications_v1_notifications_proto_rawDescGZIP(), []int{3}
}

func (x *MarkNotificationAsReadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type NotificationCountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         int64                  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotificationCountResponse) Reset() {
	*x = NotificationCountResponse{}
	mi := &file_notifications_v1_notifications_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationCountResponse) ProtoMessage() {}

func (x *NotificationCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notifications_v1_notifications_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationCountResponse.ProtoReflect.Descriptor instead.
func (*NotificationCountResponse) Descriptor() ([]byte, []int) {
	return file_notifications_v1_notifications_proto_rawDescGZIP(), []int{4}
}

func (x *NotificationCountResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type NotificationCountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotificationCountRequest) Reset() {
	*x = NotificationCountRequest{}
	mi := &file_notifications_v1_notifications_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationCountRequest) ProtoMessage() {}

func (x *NotificationCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notifications_v1_notifications_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationCountRequest.ProtoReflect.Descriptor instead.
func (*NotificationCountRequest) Descriptor() ([]byte, []int) {
	return file_notifications_v1_notifications_proto_rawDescGZIP(), []int{5}
}

type MarkNotificationAsReadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkNotificationAsReadResponse) Reset() {
	*x = MarkNotificationAsReadResponse{}
	mi := &file_notifications_v1_notifications_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkNotificationAsReadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkNotificationAsReadResponse) ProtoMessage() {}

func (x *MarkNotificationAsReadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notifications_v1_notifications_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkNotificationAsReadResponse.ProtoReflect.Descriptor instead.
func (*MarkNotificationAsReadResponse) Descriptor() ([]byte, []int) {
	return file_notifications_v1_notifications_proto_rawDescGZIP(), []int{6}
}

func (x *MarkNotificationAsReadResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *MarkNotificationAsReadResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type MarkNotificationAsUnReadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkNotificationAsUnReadRequest) Reset() {
	*x = MarkNotificationAsUnReadRequest{}
	mi := &file_notifications_v1_notifications_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkNotificationAsUnReadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkNotificationAsUnReadRequest) ProtoMessage() {}

func (x *MarkNotificationAsUnReadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notifications_v1_notifications_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkNotificationAsUnReadRequest.ProtoReflect.Descriptor instead.
func (*MarkNotificationAsUnReadRequest) Descriptor() ([]byte, []int) {
	return file_notifications_v1_notifications_proto_rawDescGZIP(), []int{7}
}

func (x *MarkNotificationAsUnReadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type MarkNotificationAsUnReadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkNotificationAsUnReadResponse) Reset() {
	*x = MarkNotificationAsUnReadResponse{}
	mi := &file_notifications_v1_notifications_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkNotificationAsUnReadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkNotificationAsUnReadResponse) ProtoMessage() {}

func (x *MarkNotificationAsUnReadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notifications_v1_notifications_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkNotificationAsUnReadResponse.ProtoReflect.Descriptor instead.
func (*MarkNotificationAsUnReadResponse) Descriptor() ([]byte, []int) {
	return file_notifications_v1_notifications_proto_rawDescGZIP(), []int{8}
}

func (x *MarkNotificationAsUnReadResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *MarkNotificationAsUnReadResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteNotificationByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNotificationByIdRequest) Reset() {
	*x = DeleteNotificationByIdRequest{}
	mi := &file_notifications_v1_notifications_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNotificationByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNotificationByIdRequest) ProtoMessage() {}

func (x *DeleteNotificationByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notifications_v1_notifications_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNotificationByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteNotificationByIdRequest) Descriptor() ([]byte, []int) {
	return file_notifications_v1_notifications_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteNotificationByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteNotificationByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNotificationByIdResponse) Reset() {
	*x = DeleteNotificationByIdResponse{}
	mi := &file_notifications_v1_notifications_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNotificationByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNotificationByIdResponse) ProtoMessage() {}

func (x *DeleteNotificationByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notifications_v1_notifications_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNotificationByIdResponse.ProtoReflect.Descriptor instead.
func (*DeleteNotificationByIdResponse) Descriptor() ([]byte, []int) {
	return file_notifications_v1_notifications_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteNotificationByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteNotificationByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_notifications_v1_notifications_proto protoreflect.FileDescriptor

const file_notifications_v1_notifications_proto_rawDesc = "" +
	"\n" +
	"$notifications/v1/notifications.proto\x12\x14api.notifications.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"o\n" +
	"\x17GetNotificationsRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\xa7\x01\n" +
	"\x1cGetUserNotificationsResponse\x126\n" +
	"\x04data\x18\x01 \x03(\v2\".api.notifications.v1.NotificationR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\xd8\x02\n" +
	"\fNotification\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x17\n" +
	"\x04json\x18\x04 \x01(\tH\x00R\x04json\x88\x01\x01\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12 \n" +
	"\tdeep_link\x18\x06 \x01(\tH\x01R\bdeepLink\x88\x01\x01\x12\x1a\n" +
	"\bseverity\x18\a \x01(\tR\bseverity\x12\x18\n" +
	"\atitleJp\x18\b \x01(\tR\atitleJp\x12$\n" +
	"\rdescriptionJp\x18\t \x01(\tR\rdescriptionJp\x12\x17\n" +
	"\ais_read\x18\n" +
	" \x01(\bR\x06isReadB\a\n" +
	"\x05_jsonB\f\n" +
	"\n" +
	"_deep_link\"/\n" +
	"\x1dMarkNotificationAsReadRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"1\n" +
	"\x19NotificationCountResponse\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x03R\x05count\"\x1a\n" +
	"\x18NotificationCountRequest\"T\n" +
	"\x1eMarkNotificationAsReadResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"1\n" +
	"\x1fMarkNotificationAsUnReadRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"V\n" +
	" MarkNotificationAsUnReadResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"/\n" +
	"\x1dDeleteNotificationByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"T\n" +
	"\x1eDeleteNotificationByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\xea\x06\n" +
	"\x14NotificationsService\x12\x81\x01\n" +
	"\x14GetUserNotifications\x12-.api.notifications.v1.GetNotificationsRequest\x1a2.api.notifications.v1.GetUserNotificationsResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x9c\x01\n" +
	"\x17GetCreatorNotifications\x12-.api.notifications.v1.GetNotificationsRequest\x1a2.api.notifications.v1.GetUserNotificationsResponse\"\x1e\x82\xb5\x18\x1a\b\x01\x18\x01\"\x14_user.vtuberId!=null\x12\x8b\x01\n" +
	"\x16MarkNotificationAsRead\x123.api.notifications.v1.MarkNotificationAsReadRequest\x1a4.api.notifications.v1.MarkNotificationAsReadResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x91\x01\n" +
	"\x18MarkNotificationAsUnRead\x125.api.notifications.v1.MarkNotificationAsUnReadRequest\x1a6.api.notifications.v1.MarkNotificationAsUnReadResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x8b\x01\n" +
	"\x16DeleteNotificationById\x123.api.notifications.v1.DeleteNotificationByIdRequest\x1a4.api.notifications.v1.DeleteNotificationByIdResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x7f\n" +
	"\x14GetNotificationCount\x12..api.notifications.v1.NotificationCountRequest\x1a/.api.notifications.v1.NotificationCountResponse\"\x06\x82\xb5\x18\x02\b\x01B@Z>github.com/nsp-inc/vtuber/api/notifications/v1;notificationsv1b\x06proto3"

var (
	file_notifications_v1_notifications_proto_rawDescOnce sync.Once
	file_notifications_v1_notifications_proto_rawDescData []byte
)

func file_notifications_v1_notifications_proto_rawDescGZIP() []byte {
	file_notifications_v1_notifications_proto_rawDescOnce.Do(func() {
		file_notifications_v1_notifications_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_notifications_v1_notifications_proto_rawDesc), len(file_notifications_v1_notifications_proto_rawDesc)))
	})
	return file_notifications_v1_notifications_proto_rawDescData
}

var file_notifications_v1_notifications_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_notifications_v1_notifications_proto_goTypes = []any{
	(*GetNotificationsRequest)(nil),          // 0: api.notifications.v1.GetNotificationsRequest
	(*GetUserNotificationsResponse)(nil),     // 1: api.notifications.v1.GetUserNotificationsResponse
	(*Notification)(nil),                     // 2: api.notifications.v1.Notification
	(*MarkNotificationAsReadRequest)(nil),    // 3: api.notifications.v1.MarkNotificationAsReadRequest
	(*NotificationCountResponse)(nil),        // 4: api.notifications.v1.NotificationCountResponse
	(*NotificationCountRequest)(nil),         // 5: api.notifications.v1.NotificationCountRequest
	(*MarkNotificationAsReadResponse)(nil),   // 6: api.notifications.v1.MarkNotificationAsReadResponse
	(*MarkNotificationAsUnReadRequest)(nil),  // 7: api.notifications.v1.MarkNotificationAsUnReadRequest
	(*MarkNotificationAsUnReadResponse)(nil), // 8: api.notifications.v1.MarkNotificationAsUnReadResponse
	(*DeleteNotificationByIdRequest)(nil),    // 9: api.notifications.v1.DeleteNotificationByIdRequest
	(*DeleteNotificationByIdResponse)(nil),   // 10: api.notifications.v1.DeleteNotificationByIdResponse
	(*v1.PaginationRequest)(nil),             // 11: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),             // 12: api.shared.v1.PaginationDetails
	(*timestamppb.Timestamp)(nil),            // 13: google.protobuf.Timestamp
}
var file_notifications_v1_notifications_proto_depIdxs = []int32{
	11, // 0: api.notifications.v1.GetNotificationsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 1: api.notifications.v1.GetUserNotificationsResponse.data:type_name -> api.notifications.v1.Notification
	12, // 2: api.notifications.v1.GetUserNotificationsResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	13, // 3: api.notifications.v1.Notification.created_at:type_name -> google.protobuf.Timestamp
	0,  // 4: api.notifications.v1.NotificationsService.GetUserNotifications:input_type -> api.notifications.v1.GetNotificationsRequest
	0,  // 5: api.notifications.v1.NotificationsService.GetCreatorNotifications:input_type -> api.notifications.v1.GetNotificationsRequest
	3,  // 6: api.notifications.v1.NotificationsService.MarkNotificationAsRead:input_type -> api.notifications.v1.MarkNotificationAsReadRequest
	7,  // 7: api.notifications.v1.NotificationsService.MarkNotificationAsUnRead:input_type -> api.notifications.v1.MarkNotificationAsUnReadRequest
	9,  // 8: api.notifications.v1.NotificationsService.DeleteNotificationById:input_type -> api.notifications.v1.DeleteNotificationByIdRequest
	5,  // 9: api.notifications.v1.NotificationsService.GetNotificationCount:input_type -> api.notifications.v1.NotificationCountRequest
	1,  // 10: api.notifications.v1.NotificationsService.GetUserNotifications:output_type -> api.notifications.v1.GetUserNotificationsResponse
	1,  // 11: api.notifications.v1.NotificationsService.GetCreatorNotifications:output_type -> api.notifications.v1.GetUserNotificationsResponse
	6,  // 12: api.notifications.v1.NotificationsService.MarkNotificationAsRead:output_type -> api.notifications.v1.MarkNotificationAsReadResponse
	8,  // 13: api.notifications.v1.NotificationsService.MarkNotificationAsUnRead:output_type -> api.notifications.v1.MarkNotificationAsUnReadResponse
	10, // 14: api.notifications.v1.NotificationsService.DeleteNotificationById:output_type -> api.notifications.v1.DeleteNotificationByIdResponse
	4,  // 15: api.notifications.v1.NotificationsService.GetNotificationCount:output_type -> api.notifications.v1.NotificationCountResponse
	10, // [10:16] is the sub-list for method output_type
	4,  // [4:10] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_notifications_v1_notifications_proto_init() }
func file_notifications_v1_notifications_proto_init() {
	if File_notifications_v1_notifications_proto != nil {
		return
	}
	file_notifications_v1_notifications_proto_msgTypes[0].OneofWrappers = []any{}
	file_notifications_v1_notifications_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_notifications_v1_notifications_proto_rawDesc), len(file_notifications_v1_notifications_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_notifications_v1_notifications_proto_goTypes,
		DependencyIndexes: file_notifications_v1_notifications_proto_depIdxs,
		MessageInfos:      file_notifications_v1_notifications_proto_msgTypes,
	}.Build()
	File_notifications_v1_notifications_proto = out.File
	file_notifications_v1_notifications_proto_goTypes = nil
	file_notifications_v1_notifications_proto_depIdxs = nil
}
