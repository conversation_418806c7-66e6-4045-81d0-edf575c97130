// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: users/v1/users.proto

package usersv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	v11 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetUserCampaignInvestmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserCampaignInvestmentRequest) Reset() {
	*x = GetUserCampaignInvestmentRequest{}
	mi := &file_users_v1_users_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserCampaignInvestmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCampaignInvestmentRequest) ProtoMessage() {}

func (x *GetUserCampaignInvestmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCampaignInvestmentRequest.ProtoReflect.Descriptor instead.
func (*GetUserCampaignInvestmentRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{0}
}

func (x *GetUserCampaignInvestmentRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetUserCampaignInvestmentRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetUserCampaignInvestmentResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*CampaignInvestment  `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetUserCampaignInvestmentResponse) Reset() {
	*x = GetUserCampaignInvestmentResponse{}
	mi := &file_users_v1_users_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserCampaignInvestmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCampaignInvestmentResponse) ProtoMessage() {}

func (x *GetUserCampaignInvestmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCampaignInvestmentResponse.ProtoReflect.Descriptor instead.
func (*GetUserCampaignInvestmentResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserCampaignInvestmentResponse) GetData() []*CampaignInvestment {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetUserCampaignInvestmentResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type CampaignInvestment struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	UserId                     int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CampaignId                 int64                  `protobuf:"varint,3,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	Amount                     int32                  `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	CreatedAt                  *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	CampaignVariantId          int64                  `protobuf:"varint,6,opt,name=campaign_variant_id,json=campaignVariantId,proto3" json:"campaign_variant_id,omitempty"`
	CampaignVariantTitle       string                 `protobuf:"bytes,7,opt,name=campaign_variant_title,json=campaignVariantTitle,proto3" json:"campaign_variant_title,omitempty"`
	CampaignVariantImage       string                 `protobuf:"bytes,8,opt,name=campaign_variant_image,json=campaignVariantImage,proto3" json:"campaign_variant_image,omitempty"`
	CampaignVariantDescription string                 `protobuf:"bytes,9,opt,name=campaign_variant_description,json=campaignVariantDescription,proto3" json:"campaign_variant_description,omitempty"`
	CampaignName               string                 `protobuf:"bytes,10,opt,name=campaign_name,json=campaignName,proto3" json:"campaign_name,omitempty"`
	CampaignThumbnail          string                 `protobuf:"bytes,11,opt,name=campaign_thumbnail,json=campaignThumbnail,proto3" json:"campaign_thumbnail,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *CampaignInvestment) Reset() {
	*x = CampaignInvestment{}
	mi := &file_users_v1_users_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CampaignInvestment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignInvestment) ProtoMessage() {}

func (x *CampaignInvestment) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignInvestment.ProtoReflect.Descriptor instead.
func (*CampaignInvestment) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{2}
}

func (x *CampaignInvestment) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CampaignInvestment) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

func (x *CampaignInvestment) GetAmount() int32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CampaignInvestment) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CampaignInvestment) GetCampaignVariantId() int64 {
	if x != nil {
		return x.CampaignVariantId
	}
	return 0
}

func (x *CampaignInvestment) GetCampaignVariantTitle() string {
	if x != nil {
		return x.CampaignVariantTitle
	}
	return ""
}

func (x *CampaignInvestment) GetCampaignVariantImage() string {
	if x != nil {
		return x.CampaignVariantImage
	}
	return ""
}

func (x *CampaignInvestment) GetCampaignVariantDescription() string {
	if x != nil {
		return x.CampaignVariantDescription
	}
	return ""
}

func (x *CampaignInvestment) GetCampaignName() string {
	if x != nil {
		return x.CampaignName
	}
	return ""
}

func (x *CampaignInvestment) GetCampaignThumbnail() string {
	if x != nil {
		return x.CampaignThumbnail
	}
	return ""
}

type GetUserPointRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPointRequest) Reset() {
	*x = GetUserPointRequest{}
	mi := &file_users_v1_users_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPointRequest) ProtoMessage() {}

func (x *GetUserPointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPointRequest.ProtoReflect.Descriptor instead.
func (*GetUserPointRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{3}
}

type GetUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Point         float32                `protobuf:"fixed32,1,opt,name=point,proto3" json:"point,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserResponse) Reset() {
	*x = GetUserResponse{}
	mi := &file_users_v1_users_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserResponse) ProtoMessage() {}

func (x *GetUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserResponse.ProtoReflect.Descriptor instead.
func (*GetUserResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{4}
}

func (x *GetUserResponse) GetPoint() float32 {
	if x != nil {
		return x.Point
	}
	return 0
}

type User struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FullName      string                 `protobuf:"bytes,2,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	Email         *string                `protobuf:"bytes,3,opt,name=email,proto3,oneof" json:"email,omitempty"`
	Image         *string                `protobuf:"bytes,4,opt,name=image,proto3,oneof" json:"image,omitempty"`
	Role          string                 `protobuf:"bytes,5,opt,name=role,proto3" json:"role,omitempty"`
	EmailVerified bool                   `protobuf:"varint,6,opt,name=email_verified,json=emailVerified,proto3" json:"email_verified,omitempty"`
	Dob           *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=dob,proto3" json:"dob,omitempty"`
	IsBanned      bool                   `protobuf:"varint,8,opt,name=is_banned,json=isBanned,proto3" json:"is_banned,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	IsVtuber      bool                   `protobuf:"varint,10,opt,name=is_vtuber,json=isVtuber,proto3" json:"is_vtuber,omitempty"`
	Vtuber        *v11.VtuberProfile     `protobuf:"bytes,11,opt,name=vtuber,proto3,oneof" json:"vtuber,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_users_v1_users_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{5}
}

func (x *User) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *User) GetImage() string {
	if x != nil && x.Image != nil {
		return *x.Image
	}
	return ""
}

func (x *User) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *User) GetEmailVerified() bool {
	if x != nil {
		return x.EmailVerified
	}
	return false
}

func (x *User) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *User) GetIsBanned() bool {
	if x != nil {
		return x.IsBanned
	}
	return false
}

func (x *User) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *User) GetIsVtuber() bool {
	if x != nil {
		return x.IsVtuber
	}
	return false
}

func (x *User) GetVtuber() *v11.VtuberProfile {
	if x != nil {
		return x.Vtuber
	}
	return nil
}

type UpdateUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *User                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserResponse) Reset() {
	*x = UpdateUserResponse{}
	mi := &file_users_v1_users_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserResponse) ProtoMessage() {}

func (x *UpdateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateUserResponse) GetData() *User {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetAllUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	Email         *string                `protobuf:"bytes,2,opt,name=email,proto3,oneof" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllUsersRequest) Reset() {
	*x = GetAllUsersRequest{}
	mi := &file_users_v1_users_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllUsersRequest) ProtoMessage() {}

func (x *GetAllUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllUsersRequest.ProtoReflect.Descriptor instead.
func (*GetAllUsersRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{7}
}

func (x *GetAllUsersRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetAllUsersRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

type GetAllUsersResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*User                `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllUsersResponse) Reset() {
	*x = GetAllUsersResponse{}
	mi := &file_users_v1_users_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllUsersResponse) ProtoMessage() {}

func (x *GetAllUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllUsersResponse.ProtoReflect.Descriptor instead.
func (*GetAllUsersResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{8}
}

func (x *GetAllUsersResponse) GetData() []*User {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllUsersResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetAllDeletedUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	Email         *string                `protobuf:"bytes,2,opt,name=email,proto3,oneof" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllDeletedUsersRequest) Reset() {
	*x = GetAllDeletedUsersRequest{}
	mi := &file_users_v1_users_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllDeletedUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllDeletedUsersRequest) ProtoMessage() {}

func (x *GetAllDeletedUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllDeletedUsersRequest.ProtoReflect.Descriptor instead.
func (*GetAllDeletedUsersRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{9}
}

func (x *GetAllDeletedUsersRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetAllDeletedUsersRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

type GetAllDeletedUsersResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*User                `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllDeletedUsersResponse) Reset() {
	*x = GetAllDeletedUsersResponse{}
	mi := &file_users_v1_users_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllDeletedUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllDeletedUsersResponse) ProtoMessage() {}

func (x *GetAllDeletedUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllDeletedUsersResponse.ProtoReflect.Descriptor instead.
func (*GetAllDeletedUsersResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{10}
}

func (x *GetAllDeletedUsersResponse) GetData() []*User {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllDeletedUsersResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetUserByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserByIdRequest) Reset() {
	*x = GetUserByIdRequest{}
	mi := &file_users_v1_users_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByIdRequest) ProtoMessage() {}

func (x *GetUserByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByIdRequest.ProtoReflect.Descriptor instead.
func (*GetUserByIdRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{11}
}

func (x *GetUserByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *User                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserByIdResponse) Reset() {
	*x = GetUserByIdResponse{}
	mi := &file_users_v1_users_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByIdResponse) ProtoMessage() {}

func (x *GetUserByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByIdResponse.ProtoReflect.Descriptor instead.
func (*GetUserByIdResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{12}
}

func (x *GetUserByIdResponse) GetData() *User {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteUserByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserByIdRequest) Reset() {
	*x = DeleteUserByIdRequest{}
	mi := &file_users_v1_users_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserByIdRequest) ProtoMessage() {}

func (x *DeleteUserByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserByIdRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteUserByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateUserByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FullName      string                 `protobuf:"bytes,1,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty" validate:"required"`  
	Image         string                 `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
	Id            int64                  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserByIdRequest) Reset() {
	*x = UpdateUserByIdRequest{}
	mi := &file_users_v1_users_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserByIdRequest) ProtoMessage() {}

func (x *UpdateUserByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserByIdRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateUserByIdRequest) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *UpdateUserByIdRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UpdateUserByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type BanUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BanUserRequest) Reset() {
	*x = BanUserRequest{}
	mi := &file_users_v1_users_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BanUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BanUserRequest) ProtoMessage() {}

func (x *BanUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BanUserRequest.ProtoReflect.Descriptor instead.
func (*BanUserRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{15}
}

func (x *BanUserRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_users_v1_users_proto protoreflect.FileDescriptor

const file_users_v1_users_proto_rawDesc = "" +
	"\n" +
	"\x14users/v1/users.proto\x12\fapi.users.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\x1a\x1fvtubers/v1/vtuberprofiles.proto\"\x91\x01\n" +
	" GetUserCampaignInvestmentRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userIdB\r\n" +
	"\v_pagination\"\xaa\x01\n" +
	"!GetUserCampaignInvestmentResponse\x124\n" +
	"\x04data\x18\x01 \x03(\v2 .api.users.v1.CampaignInvestmentR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\xd3\x03\n" +
	"\x12CampaignInvestment\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\x12\x1f\n" +
	"\vcampaign_id\x18\x03 \x01(\x03R\n" +
	"campaignId\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\x05R\x06amount\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12.\n" +
	"\x13campaign_variant_id\x18\x06 \x01(\x03R\x11campaignVariantId\x124\n" +
	"\x16campaign_variant_title\x18\a \x01(\tR\x14campaignVariantTitle\x124\n" +
	"\x16campaign_variant_image\x18\b \x01(\tR\x14campaignVariantImage\x12@\n" +
	"\x1ccampaign_variant_description\x18\t \x01(\tR\x1acampaignVariantDescription\x12#\n" +
	"\rcampaign_name\x18\n" +
	" \x01(\tR\fcampaignName\x12-\n" +
	"\x12campaign_thumbnail\x18\v \x01(\tR\x11campaignThumbnail\"\x15\n" +
	"\x13GetUserPointRequest\"'\n" +
	"\x0fGetUserResponse\x12\x14\n" +
	"\x05point\x18\x01 \x01(\x02R\x05point\"\xa2\x03\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tfull_name\x18\x02 \x01(\tR\bfullName\x12\x19\n" +
	"\x05email\x18\x03 \x01(\tH\x00R\x05email\x88\x01\x01\x12\x19\n" +
	"\x05image\x18\x04 \x01(\tH\x01R\x05image\x88\x01\x01\x12\x12\n" +
	"\x04role\x18\x05 \x01(\tR\x04role\x12%\n" +
	"\x0eemail_verified\x18\x06 \x01(\bR\remailVerified\x12,\n" +
	"\x03dob\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x03dob\x12\x1b\n" +
	"\tis_banned\x18\b \x01(\bR\bisBanned\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1b\n" +
	"\tis_vtuber\x18\n" +
	" \x01(\bR\bisVtuber\x12:\n" +
	"\x06vtuber\x18\v \x01(\v2\x1d.api.vtubers.v1.VtuberProfileH\x02R\x06vtuber\x88\x01\x01B\b\n" +
	"\x06_emailB\b\n" +
	"\x06_imageB\t\n" +
	"\a_vtuber\"<\n" +
	"\x12UpdateUserResponse\x12&\n" +
	"\x04data\x18\x01 \x01(\v2\x12.api.users.v1.UserR\x04data\"\x8f\x01\n" +
	"\x12GetAllUsersRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01\x12\x19\n" +
	"\x05email\x18\x02 \x01(\tH\x01R\x05email\x88\x01\x01B\r\n" +
	"\v_paginationB\b\n" +
	"\x06_email\"\x8e\x01\n" +
	"\x13GetAllUsersResponse\x12&\n" +
	"\x04data\x18\x01 \x03(\v2\x12.api.users.v1.UserR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\x96\x01\n" +
	"\x19GetAllDeletedUsersRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01\x12\x19\n" +
	"\x05email\x18\x02 \x01(\tH\x01R\x05email\x88\x01\x01B\r\n" +
	"\v_paginationB\b\n" +
	"\x06_email\"\x95\x01\n" +
	"\x1aGetAllDeletedUsersResponse\x12&\n" +
	"\x04data\x18\x01 \x03(\v2\x12.api.users.v1.UserR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"$\n" +
	"\x12GetUserByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"=\n" +
	"\x13GetUserByIdResponse\x12&\n" +
	"\x04data\x18\x01 \x01(\v2\x12.api.users.v1.UserR\x04data\"'\n" +
	"\x15DeleteUserByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"Z\n" +
	"\x15UpdateUserByIdRequest\x12\x1b\n" +
	"\tfull_name\x18\x01 \x01(\tR\bfullName\x12\x14\n" +
	"\x05image\x18\x02 \x01(\tR\x05image\x12\x0e\n" +
	"\x02id\x18\x03 \x01(\x03R\x02id\" \n" +
	"\x0eBanUserRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id2\xc0\a\n" +
	"\vUserService\x12\\\n" +
	"\vGetAllUsers\x12 .api.users.v1.GetAllUsersRequest\x1a!.api.users.v1.GetAllUsersResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12q\n" +
	"\x12GetAllDeletedUsers\x12'.api.users.v1.GetAllDeletedUsersRequest\x1a(.api.users.v1.GetAllDeletedUsersResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12Z\n" +
	"\vGetUserById\x12 .api.users.v1.GetUserByIdRequest\x1a!.api.users.v1.GetUserByIdResponse\"\x06\x82\xb5\x18\x02\b\x01\x12_\n" +
	"\x0eDeleteUserById\x12#.api.users.v1.DeleteUserByIdRequest\x1a\x1e.api.shared.v1.GenericResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12\x88\x01\n" +
	"\x0eUpdateUserById\x12#.api.users.v1.UpdateUserByIdRequest\x1a .api.users.v1.UpdateUserResponse\"/\x82\xb5\x18+\b\x01\"'_user.role == 'admin' || _user.id == id\x12Q\n" +
	"\aBanUser\x12\x1c.api.users.v1.BanUserRequest\x1a\x1e.api.shared.v1.GenericResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12b\n" +
	"\x11GetAllBannedUsers\x12 .api.users.v1.GetAllUsersRequest\x1a!.api.users.v1.GetAllUsersResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12X\n" +
	"\fGetUserPoint\x12!.api.users.v1.GetUserPointRequest\x1a\x1d.api.users.v1.GetUserResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x86\x01\n" +
	"\x19GetUserCampaignInvestment\x12..api.users.v1.GetUserCampaignInvestmentRequest\x1a/.api.users.v1.GetUserCampaignInvestmentResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01B0Z.github.com/nsp-inc/vtuber/api/users/v1;usersv1b\x06proto3"

var (
	file_users_v1_users_proto_rawDescOnce sync.Once
	file_users_v1_users_proto_rawDescData []byte
)

func file_users_v1_users_proto_rawDescGZIP() []byte {
	file_users_v1_users_proto_rawDescOnce.Do(func() {
		file_users_v1_users_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_users_v1_users_proto_rawDesc), len(file_users_v1_users_proto_rawDesc)))
	})
	return file_users_v1_users_proto_rawDescData
}

var file_users_v1_users_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_users_v1_users_proto_goTypes = []any{
	(*GetUserCampaignInvestmentRequest)(nil),  // 0: api.users.v1.GetUserCampaignInvestmentRequest
	(*GetUserCampaignInvestmentResponse)(nil), // 1: api.users.v1.GetUserCampaignInvestmentResponse
	(*CampaignInvestment)(nil),                // 2: api.users.v1.CampaignInvestment
	(*GetUserPointRequest)(nil),               // 3: api.users.v1.GetUserPointRequest
	(*GetUserResponse)(nil),                   // 4: api.users.v1.GetUserResponse
	(*User)(nil),                              // 5: api.users.v1.User
	(*UpdateUserResponse)(nil),                // 6: api.users.v1.UpdateUserResponse
	(*GetAllUsersRequest)(nil),                // 7: api.users.v1.GetAllUsersRequest
	(*GetAllUsersResponse)(nil),               // 8: api.users.v1.GetAllUsersResponse
	(*GetAllDeletedUsersRequest)(nil),         // 9: api.users.v1.GetAllDeletedUsersRequest
	(*GetAllDeletedUsersResponse)(nil),        // 10: api.users.v1.GetAllDeletedUsersResponse
	(*GetUserByIdRequest)(nil),                // 11: api.users.v1.GetUserByIdRequest
	(*GetUserByIdResponse)(nil),               // 12: api.users.v1.GetUserByIdResponse
	(*DeleteUserByIdRequest)(nil),             // 13: api.users.v1.DeleteUserByIdRequest
	(*UpdateUserByIdRequest)(nil),             // 14: api.users.v1.UpdateUserByIdRequest
	(*BanUserRequest)(nil),                    // 15: api.users.v1.BanUserRequest
	(*v1.PaginationRequest)(nil),              // 16: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),              // 17: api.shared.v1.PaginationDetails
	(*timestamppb.Timestamp)(nil),             // 18: google.protobuf.Timestamp
	(*v11.VtuberProfile)(nil),                 // 19: api.vtubers.v1.VtuberProfile
	(*v1.GenericResponse)(nil),                // 20: api.shared.v1.GenericResponse
}
var file_users_v1_users_proto_depIdxs = []int32{
	16, // 0: api.users.v1.GetUserCampaignInvestmentRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 1: api.users.v1.GetUserCampaignInvestmentResponse.data:type_name -> api.users.v1.CampaignInvestment
	17, // 2: api.users.v1.GetUserCampaignInvestmentResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	18, // 3: api.users.v1.CampaignInvestment.created_at:type_name -> google.protobuf.Timestamp
	18, // 4: api.users.v1.User.dob:type_name -> google.protobuf.Timestamp
	18, // 5: api.users.v1.User.created_at:type_name -> google.protobuf.Timestamp
	19, // 6: api.users.v1.User.vtuber:type_name -> api.vtubers.v1.VtuberProfile
	5,  // 7: api.users.v1.UpdateUserResponse.data:type_name -> api.users.v1.User
	16, // 8: api.users.v1.GetAllUsersRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	5,  // 9: api.users.v1.GetAllUsersResponse.data:type_name -> api.users.v1.User
	17, // 10: api.users.v1.GetAllUsersResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	16, // 11: api.users.v1.GetAllDeletedUsersRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	5,  // 12: api.users.v1.GetAllDeletedUsersResponse.data:type_name -> api.users.v1.User
	17, // 13: api.users.v1.GetAllDeletedUsersResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	5,  // 14: api.users.v1.GetUserByIdResponse.data:type_name -> api.users.v1.User
	7,  // 15: api.users.v1.UserService.GetAllUsers:input_type -> api.users.v1.GetAllUsersRequest
	9,  // 16: api.users.v1.UserService.GetAllDeletedUsers:input_type -> api.users.v1.GetAllDeletedUsersRequest
	11, // 17: api.users.v1.UserService.GetUserById:input_type -> api.users.v1.GetUserByIdRequest
	13, // 18: api.users.v1.UserService.DeleteUserById:input_type -> api.users.v1.DeleteUserByIdRequest
	14, // 19: api.users.v1.UserService.UpdateUserById:input_type -> api.users.v1.UpdateUserByIdRequest
	15, // 20: api.users.v1.UserService.BanUser:input_type -> api.users.v1.BanUserRequest
	7,  // 21: api.users.v1.UserService.GetAllBannedUsers:input_type -> api.users.v1.GetAllUsersRequest
	3,  // 22: api.users.v1.UserService.GetUserPoint:input_type -> api.users.v1.GetUserPointRequest
	0,  // 23: api.users.v1.UserService.GetUserCampaignInvestment:input_type -> api.users.v1.GetUserCampaignInvestmentRequest
	8,  // 24: api.users.v1.UserService.GetAllUsers:output_type -> api.users.v1.GetAllUsersResponse
	10, // 25: api.users.v1.UserService.GetAllDeletedUsers:output_type -> api.users.v1.GetAllDeletedUsersResponse
	12, // 26: api.users.v1.UserService.GetUserById:output_type -> api.users.v1.GetUserByIdResponse
	20, // 27: api.users.v1.UserService.DeleteUserById:output_type -> api.shared.v1.GenericResponse
	6,  // 28: api.users.v1.UserService.UpdateUserById:output_type -> api.users.v1.UpdateUserResponse
	20, // 29: api.users.v1.UserService.BanUser:output_type -> api.shared.v1.GenericResponse
	8,  // 30: api.users.v1.UserService.GetAllBannedUsers:output_type -> api.users.v1.GetAllUsersResponse
	4,  // 31: api.users.v1.UserService.GetUserPoint:output_type -> api.users.v1.GetUserResponse
	1,  // 32: api.users.v1.UserService.GetUserCampaignInvestment:output_type -> api.users.v1.GetUserCampaignInvestmentResponse
	24, // [24:33] is the sub-list for method output_type
	15, // [15:24] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_users_v1_users_proto_init() }
func file_users_v1_users_proto_init() {
	if File_users_v1_users_proto != nil {
		return
	}
	file_users_v1_users_proto_msgTypes[0].OneofWrappers = []any{}
	file_users_v1_users_proto_msgTypes[5].OneofWrappers = []any{}
	file_users_v1_users_proto_msgTypes[7].OneofWrappers = []any{}
	file_users_v1_users_proto_msgTypes[9].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_users_v1_users_proto_rawDesc), len(file_users_v1_users_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_users_v1_users_proto_goTypes,
		DependencyIndexes: file_users_v1_users_proto_depIdxs,
		MessageInfos:      file_users_v1_users_proto_msgTypes,
	}.Build()
	File_users_v1_users_proto = out.File
	file_users_v1_users_proto_goTypes = nil
	file_users_v1_users_proto_depIdxs = nil
}
