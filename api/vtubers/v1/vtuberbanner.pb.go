// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: vtubers/v1/vtuberbanner.proto

package vtubersv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	_ "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddVtuberBannerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Image         string                 `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddVtuberBannerRequest) Reset() {
	*x = AddVtuberBannerRequest{}
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberBannerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberBannerRequest) ProtoMessage() {}

func (x *AddVtuberBannerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberBannerRequest.ProtoReflect.Descriptor instead.
func (*AddVtuberBannerRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberbanner_proto_rawDescGZIP(), []int{0}
}

func (x *AddVtuberBannerRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type VtuberBanner struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	VtuberId      int64                  `protobuf:"varint,2,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty"`
	Image         string                 `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VtuberBanner) Reset() {
	*x = VtuberBanner{}
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VtuberBanner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VtuberBanner) ProtoMessage() {}

func (x *VtuberBanner) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VtuberBanner.ProtoReflect.Descriptor instead.
func (*VtuberBanner) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberbanner_proto_rawDescGZIP(), []int{1}
}

func (x *VtuberBanner) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VtuberBanner) GetVtuberId() int64 {
	if x != nil {
		return x.VtuberId
	}
	return 0
}

func (x *VtuberBanner) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *VtuberBanner) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VtuberBanner) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AddVtuberBannerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *VtuberBanner          `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddVtuberBannerResponse) Reset() {
	*x = AddVtuberBannerResponse{}
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberBannerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberBannerResponse) ProtoMessage() {}

func (x *AddVtuberBannerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberBannerResponse.ProtoReflect.Descriptor instead.
func (*AddVtuberBannerResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberbanner_proto_rawDescGZIP(), []int{2}
}

func (x *AddVtuberBannerResponse) GetData() *VtuberBanner {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteVtuberBannerByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteVtuberBannerByIdRequest) Reset() {
	*x = DeleteVtuberBannerByIdRequest{}
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteVtuberBannerByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVtuberBannerByIdRequest) ProtoMessage() {}

func (x *DeleteVtuberBannerByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVtuberBannerByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteVtuberBannerByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberbanner_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteVtuberBannerByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateVtuberBannerByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`       
	Image         string                 `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVtuberBannerByIdRequest) Reset() {
	*x = UpdateVtuberBannerByIdRequest{}
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVtuberBannerByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVtuberBannerByIdRequest) ProtoMessage() {}

func (x *UpdateVtuberBannerByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVtuberBannerByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdateVtuberBannerByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberbanner_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateVtuberBannerByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateVtuberBannerByIdRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type GetVtuberBannerByVtuberIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VtuberId      string                 `protobuf:"bytes,1,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberBannerByVtuberIdRequest) Reset() {
	*x = GetVtuberBannerByVtuberIdRequest{}
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberBannerByVtuberIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberBannerByVtuberIdRequest) ProtoMessage() {}

func (x *GetVtuberBannerByVtuberIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberBannerByVtuberIdRequest.ProtoReflect.Descriptor instead.
func (*GetVtuberBannerByVtuberIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberbanner_proto_rawDescGZIP(), []int{5}
}

func (x *GetVtuberBannerByVtuberIdRequest) GetVtuberId() string {
	if x != nil {
		return x.VtuberId
	}
	return ""
}

type GetVtuberBannerByVtuberIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*VtuberBanner        `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberBannerByVtuberIdResponse) Reset() {
	*x = GetVtuberBannerByVtuberIdResponse{}
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberBannerByVtuberIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberBannerByVtuberIdResponse) ProtoMessage() {}

func (x *GetVtuberBannerByVtuberIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberBannerByVtuberIdResponse.ProtoReflect.Descriptor instead.
func (*GetVtuberBannerByVtuberIdResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberbanner_proto_rawDescGZIP(), []int{6}
}

func (x *GetVtuberBannerByVtuberIdResponse) GetData() []*VtuberBanner {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteVtuberBannerByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteVtuberBannerByIdResponse) Reset() {
	*x = DeleteVtuberBannerByIdResponse{}
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteVtuberBannerByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVtuberBannerByIdResponse) ProtoMessage() {}

func (x *DeleteVtuberBannerByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVtuberBannerByIdResponse.ProtoReflect.Descriptor instead.
func (*DeleteVtuberBannerByIdResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberbanner_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteVtuberBannerByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteVtuberBannerByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateVtuberBannerByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVtuberBannerByIdResponse) Reset() {
	*x = UpdateVtuberBannerByIdResponse{}
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVtuberBannerByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVtuberBannerByIdResponse) ProtoMessage() {}

func (x *UpdateVtuberBannerByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberbanner_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVtuberBannerByIdResponse.ProtoReflect.Descriptor instead.
func (*UpdateVtuberBannerByIdResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberbanner_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateVtuberBannerByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateVtuberBannerByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_vtubers_v1_vtuberbanner_proto protoreflect.FileDescriptor

const file_vtubers_v1_vtuberbanner_proto_rawDesc = "" +
	"\n" +
	"\x1dvtubers/v1/vtuberbanner.proto\x12\x0eapi.vtubers.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\".\n" +
	"\x16AddVtuberBannerRequest\x12\x14\n" +
	"\x05image\x18\x01 \x01(\tR\x05image\"\xc7\x01\n" +
	"\fVtuberBanner\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tvtuber_id\x18\x02 \x01(\x03R\bvtuberId\x12\x14\n" +
	"\x05image\x18\x03 \x01(\tR\x05image\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"K\n" +
	"\x17AddVtuberBannerResponse\x120\n" +
	"\x04data\x18\x01 \x01(\v2\x1c.api.vtubers.v1.VtuberBannerR\x04data\"/\n" +
	"\x1dDeleteVtuberBannerByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"E\n" +
	"\x1dUpdateVtuberBannerByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05image\x18\x02 \x01(\tR\x05image\"?\n" +
	" GetVtuberBannerByVtuberIdRequest\x12\x1b\n" +
	"\tvtuber_id\x18\x01 \x01(\tR\bvtuberId\"U\n" +
	"!GetVtuberBannerByVtuberIdResponse\x120\n" +
	"\x04data\x18\x01 \x03(\v2\x1c.api.vtubers.v1.VtuberBannerR\x04data\"T\n" +
	"\x1eDeleteVtuberBannerByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"T\n" +
	"\x1eUpdateVtuberBannerByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\x8e\x04\n" +
	"\x13VtuberBannerService\x12l\n" +
	"\x0fAddVtuberBanner\x12&.api.vtubers.v1.AddVtuberBannerRequest\x1a'.api.vtubers.v1.AddVtuberBannerResponse\"\b\x82\xb5\x18\x04\b\x01\x18\x01\x12\x80\x01\n" +
	"\x19GetVtuberBannerByVtuberId\x120.api.vtubers.v1.GetVtuberBannerByVtuberIdRequest\x1a1.api.vtubers.v1.GetVtuberBannerByVtuberIdResponse\x12\x81\x01\n" +
	"\x16DeleteVtuberBannerById\x12-.api.vtubers.v1.DeleteVtuberBannerByIdRequest\x1a..api.vtubers.v1.DeleteVtuberBannerByIdResponse\"\b\x82\xb5\x18\x04\b\x01\x18\x01\x12\x81\x01\n" +
	"\x16UpdateVtuberBannerById\x12-.api.vtubers.v1.UpdateVtuberBannerByIdRequest\x1a..api.vtubers.v1.UpdateVtuberBannerByIdResponse\"\b\x82\xb5\x18\x04\b\x01\x18\x01B4Z2github.com/nsp-inc/vtuber/api/vtubers/v1;vtubersv1b\x06proto3"

var (
	file_vtubers_v1_vtuberbanner_proto_rawDescOnce sync.Once
	file_vtubers_v1_vtuberbanner_proto_rawDescData []byte
)

func file_vtubers_v1_vtuberbanner_proto_rawDescGZIP() []byte {
	file_vtubers_v1_vtuberbanner_proto_rawDescOnce.Do(func() {
		file_vtubers_v1_vtuberbanner_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtuberbanner_proto_rawDesc), len(file_vtubers_v1_vtuberbanner_proto_rawDesc)))
	})
	return file_vtubers_v1_vtuberbanner_proto_rawDescData
}

var file_vtubers_v1_vtuberbanner_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_vtubers_v1_vtuberbanner_proto_goTypes = []any{
	(*AddVtuberBannerRequest)(nil),            // 0: api.vtubers.v1.AddVtuberBannerRequest
	(*VtuberBanner)(nil),                      // 1: api.vtubers.v1.VtuberBanner
	(*AddVtuberBannerResponse)(nil),           // 2: api.vtubers.v1.AddVtuberBannerResponse
	(*DeleteVtuberBannerByIdRequest)(nil),     // 3: api.vtubers.v1.DeleteVtuberBannerByIdRequest
	(*UpdateVtuberBannerByIdRequest)(nil),     // 4: api.vtubers.v1.UpdateVtuberBannerByIdRequest
	(*GetVtuberBannerByVtuberIdRequest)(nil),  // 5: api.vtubers.v1.GetVtuberBannerByVtuberIdRequest
	(*GetVtuberBannerByVtuberIdResponse)(nil), // 6: api.vtubers.v1.GetVtuberBannerByVtuberIdResponse
	(*DeleteVtuberBannerByIdResponse)(nil),    // 7: api.vtubers.v1.DeleteVtuberBannerByIdResponse
	(*UpdateVtuberBannerByIdResponse)(nil),    // 8: api.vtubers.v1.UpdateVtuberBannerByIdResponse
	(*timestamppb.Timestamp)(nil),             // 9: google.protobuf.Timestamp
}
var file_vtubers_v1_vtuberbanner_proto_depIdxs = []int32{
	9, // 0: api.vtubers.v1.VtuberBanner.created_at:type_name -> google.protobuf.Timestamp
	9, // 1: api.vtubers.v1.VtuberBanner.updated_at:type_name -> google.protobuf.Timestamp
	1, // 2: api.vtubers.v1.AddVtuberBannerResponse.data:type_name -> api.vtubers.v1.VtuberBanner
	1, // 3: api.vtubers.v1.GetVtuberBannerByVtuberIdResponse.data:type_name -> api.vtubers.v1.VtuberBanner
	0, // 4: api.vtubers.v1.VtuberBannerService.AddVtuberBanner:input_type -> api.vtubers.v1.AddVtuberBannerRequest
	5, // 5: api.vtubers.v1.VtuberBannerService.GetVtuberBannerByVtuberId:input_type -> api.vtubers.v1.GetVtuberBannerByVtuberIdRequest
	3, // 6: api.vtubers.v1.VtuberBannerService.DeleteVtuberBannerById:input_type -> api.vtubers.v1.DeleteVtuberBannerByIdRequest
	4, // 7: api.vtubers.v1.VtuberBannerService.UpdateVtuberBannerById:input_type -> api.vtubers.v1.UpdateVtuberBannerByIdRequest
	2, // 8: api.vtubers.v1.VtuberBannerService.AddVtuberBanner:output_type -> api.vtubers.v1.AddVtuberBannerResponse
	6, // 9: api.vtubers.v1.VtuberBannerService.GetVtuberBannerByVtuberId:output_type -> api.vtubers.v1.GetVtuberBannerByVtuberIdResponse
	7, // 10: api.vtubers.v1.VtuberBannerService.DeleteVtuberBannerById:output_type -> api.vtubers.v1.DeleteVtuberBannerByIdResponse
	8, // 11: api.vtubers.v1.VtuberBannerService.UpdateVtuberBannerById:output_type -> api.vtubers.v1.UpdateVtuberBannerByIdResponse
	8, // [8:12] is the sub-list for method output_type
	4, // [4:8] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_vtubers_v1_vtuberbanner_proto_init() }
func file_vtubers_v1_vtuberbanner_proto_init() {
	if File_vtubers_v1_vtuberbanner_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtuberbanner_proto_rawDesc), len(file_vtubers_v1_vtuberbanner_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_vtubers_v1_vtuberbanner_proto_goTypes,
		DependencyIndexes: file_vtubers_v1_vtuberbanner_proto_depIdxs,
		MessageInfos:      file_vtubers_v1_vtuberbanner_proto_msgTypes,
	}.Build()
	File_vtubers_v1_vtuberbanner_proto = out.File
	file_vtubers_v1_vtuberbanner_proto_goTypes = nil
	file_vtubers_v1_vtuberbanner_proto_depIdxs = nil
}
