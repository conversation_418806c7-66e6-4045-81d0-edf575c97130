// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: vtubers/v1/vtuberplan.proto

package vtubersv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddVtuberPlanRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Title            string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty" validate:"required"`                                                
	Description      string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty" validate:"required"`                                    
	Price            int32                  `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty" validate:"gte=1"`                                               
	Index            int32                  `protobuf:"varint,5,opt,name=index,proto3" json:"index,omitempty" validate:"gte=1"`                                               
	ShortDescription string                 `protobuf:"bytes,6,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty" validate:"required"`  
	AnnualPrice      int32                  `protobuf:"varint,7,opt,name=annual_price,json=annualPrice,proto3" json:"annual_price,omitempty" validate:"gte=1"`                
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AddVtuberPlanRequest) Reset() {
	*x = AddVtuberPlanRequest{}
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberPlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberPlanRequest) ProtoMessage() {}

func (x *AddVtuberPlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberPlanRequest.ProtoReflect.Descriptor instead.
func (*AddVtuberPlanRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberplan_proto_rawDescGZIP(), []int{0}
}

func (x *AddVtuberPlanRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AddVtuberPlanRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AddVtuberPlanRequest) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *AddVtuberPlanRequest) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *AddVtuberPlanRequest) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *AddVtuberPlanRequest) GetAnnualPrice() int32 {
	if x != nil {
		return x.AnnualPrice
	}
	return 0
}

type AddVtuberPlanResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *VtuberPlan            `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddVtuberPlanResponse) Reset() {
	*x = AddVtuberPlanResponse{}
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberPlanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberPlanResponse) ProtoMessage() {}

func (x *AddVtuberPlanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberPlanResponse.ProtoReflect.Descriptor instead.
func (*AddVtuberPlanResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberplan_proto_rawDescGZIP(), []int{1}
}

func (x *AddVtuberPlanResponse) GetData() *VtuberPlan {
	if x != nil {
		return x.Data
	}
	return nil
}

type VtuberPlan struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title            string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description      string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Price            int32                  `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	VtuberId         int64                  `protobuf:"varint,5,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty"`
	Index            int32                  `protobuf:"varint,6,opt,name=index,proto3" json:"index,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	IsSubscribed     bool                   `protobuf:"varint,8,opt,name=is_subscribed,json=isSubscribed,proto3" json:"is_subscribed,omitempty"`
	ShortDescription string                 `protobuf:"bytes,9,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty"`
	AnnualPrice      int32                  `protobuf:"varint,10,opt,name=annual_price,json=annualPrice,proto3" json:"annual_price,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *VtuberPlan) Reset() {
	*x = VtuberPlan{}
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VtuberPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VtuberPlan) ProtoMessage() {}

func (x *VtuberPlan) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VtuberPlan.ProtoReflect.Descriptor instead.
func (*VtuberPlan) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberplan_proto_rawDescGZIP(), []int{2}
}

func (x *VtuberPlan) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VtuberPlan) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *VtuberPlan) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VtuberPlan) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *VtuberPlan) GetVtuberId() int64 {
	if x != nil {
		return x.VtuberId
	}
	return 0
}

func (x *VtuberPlan) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *VtuberPlan) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VtuberPlan) GetIsSubscribed() bool {
	if x != nil {
		return x.IsSubscribed
	}
	return false
}

func (x *VtuberPlan) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *VtuberPlan) GetAnnualPrice() int32 {
	if x != nil {
		return x.AnnualPrice
	}
	return 0
}

type GetAllVtuberPlansByVtuberIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VtuberId      int64                  `protobuf:"varint,1,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllVtuberPlansByVtuberIdRequest) Reset() {
	*x = GetAllVtuberPlansByVtuberIdRequest{}
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberPlansByVtuberIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberPlansByVtuberIdRequest) ProtoMessage() {}

func (x *GetAllVtuberPlansByVtuberIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberPlansByVtuberIdRequest.ProtoReflect.Descriptor instead.
func (*GetAllVtuberPlansByVtuberIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberplan_proto_rawDescGZIP(), []int{3}
}

func (x *GetAllVtuberPlansByVtuberIdRequest) GetVtuberId() int64 {
	if x != nil {
		return x.VtuberId
	}
	return 0
}

type GetAllVtuberPlansResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VtuberPlan    []*VtuberPlan          `protobuf:"bytes,1,rep,name=VtuberPlan,proto3" json:"VtuberPlan,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllVtuberPlansResponse) Reset() {
	*x = GetAllVtuberPlansResponse{}
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberPlansResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberPlansResponse) ProtoMessage() {}

func (x *GetAllVtuberPlansResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberPlansResponse.ProtoReflect.Descriptor instead.
func (*GetAllVtuberPlansResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberplan_proto_rawDescGZIP(), []int{4}
}

func (x *GetAllVtuberPlansResponse) GetVtuberPlan() []*VtuberPlan {
	if x != nil {
		return x.VtuberPlan
	}
	return nil
}

type GetVtuberPlanByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberPlanByIdRequest) Reset() {
	*x = GetVtuberPlanByIdRequest{}
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberPlanByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberPlanByIdRequest) ProtoMessage() {}

func (x *GetVtuberPlanByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberPlanByIdRequest.ProtoReflect.Descriptor instead.
func (*GetVtuberPlanByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberplan_proto_rawDescGZIP(), []int{5}
}

func (x *GetVtuberPlanByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetVtuberPlanByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *VtuberPlan            `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberPlanByIdResponse) Reset() {
	*x = GetVtuberPlanByIdResponse{}
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberPlanByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberPlanByIdResponse) ProtoMessage() {}

func (x *GetVtuberPlanByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberPlanByIdResponse.ProtoReflect.Descriptor instead.
func (*GetVtuberPlanByIdResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberplan_proto_rawDescGZIP(), []int{6}
}

func (x *GetVtuberPlanByIdResponse) GetData() *VtuberPlan {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteVtuberPlanByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteVtuberPlanByIdRequest) Reset() {
	*x = DeleteVtuberPlanByIdRequest{}
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteVtuberPlanByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVtuberPlanByIdRequest) ProtoMessage() {}

func (x *DeleteVtuberPlanByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVtuberPlanByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteVtuberPlanByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberplan_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteVtuberPlanByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateVtuberPlanByIdRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Title            string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty" validate:"required"`                                                
	Description      string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty" validate:"required"`                                    
	Id               int64                  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty" validate:"required"`                                                     
	Price            int32                  `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty" validate:"gte=1"`                                               
	Index            int32                  `protobuf:"varint,6,opt,name=index,proto3" json:"index,omitempty" validate:"gte=1"`                                               
	ShortDescription string                 `protobuf:"bytes,7,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty" validate:"required"`  
	AnnualPrice      int32                  `protobuf:"varint,8,opt,name=annual_price,json=annualPrice,proto3" json:"annual_price,omitempty" validate:"gte=1"`                
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UpdateVtuberPlanByIdRequest) Reset() {
	*x = UpdateVtuberPlanByIdRequest{}
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVtuberPlanByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVtuberPlanByIdRequest) ProtoMessage() {}

func (x *UpdateVtuberPlanByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberplan_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVtuberPlanByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdateVtuberPlanByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberplan_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateVtuberPlanByIdRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateVtuberPlanByIdRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateVtuberPlanByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateVtuberPlanByIdRequest) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *UpdateVtuberPlanByIdRequest) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *UpdateVtuberPlanByIdRequest) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *UpdateVtuberPlanByIdRequest) GetAnnualPrice() int32 {
	if x != nil {
		return x.AnnualPrice
	}
	return 0
}

var File_vtubers_v1_vtuberplan_proto protoreflect.FileDescriptor

const file_vtubers_v1_vtuberplan_proto_rawDesc = "" +
	"\n" +
	"\x1bvtubers/v1/vtuberplan.proto\x12\x0eapi.vtubers.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"\xca\x01\n" +
	"\x14AddVtuberPlanRequest\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x14\n" +
	"\x05price\x18\x04 \x01(\x05R\x05price\x12\x14\n" +
	"\x05index\x18\x05 \x01(\x05R\x05index\x12+\n" +
	"\x11short_description\x18\x06 \x01(\tR\x10shortDescription\x12!\n" +
	"\fannual_price\x18\a \x01(\x05R\vannualPrice\"G\n" +
	"\x15AddVtuberPlanResponse\x12.\n" +
	"\x04data\x18\x01 \x01(\v2\x1a.api.vtubers.v1.VtuberPlanR\x04data\"\xcd\x02\n" +
	"\n" +
	"VtuberPlan\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x14\n" +
	"\x05price\x18\x04 \x01(\x05R\x05price\x12\x1b\n" +
	"\tvtuber_id\x18\x05 \x01(\x03R\bvtuberId\x12\x14\n" +
	"\x05index\x18\x06 \x01(\x05R\x05index\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12#\n" +
	"\ris_subscribed\x18\b \x01(\bR\fisSubscribed\x12+\n" +
	"\x11short_description\x18\t \x01(\tR\x10shortDescription\x12!\n" +
	"\fannual_price\x18\n" +
	" \x01(\x05R\vannualPrice\"A\n" +
	"\"GetAllVtuberPlansByVtuberIdRequest\x12\x1b\n" +
	"\tvtuber_id\x18\x01 \x01(\x03R\bvtuberId\"W\n" +
	"\x19GetAllVtuberPlansResponse\x12:\n" +
	"\n" +
	"VtuberPlan\x18\x01 \x03(\v2\x1a.api.vtubers.v1.VtuberPlanR\n" +
	"VtuberPlan\"*\n" +
	"\x18GetVtuberPlanByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"K\n" +
	"\x19GetVtuberPlanByIdResponse\x12.\n" +
	"\x04data\x18\x01 \x01(\v2\x1a.api.vtubers.v1.VtuberPlanR\x04data\"-\n" +
	"\x1bDeleteVtuberPlanByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xe1\x01\n" +
	"\x1bUpdateVtuberPlanByIdRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x0e\n" +
	"\x02id\x18\x03 \x01(\x03R\x02id\x12\x14\n" +
	"\x05price\x18\x04 \x01(\x05R\x05price\x12\x14\n" +
	"\x05index\x18\x06 \x01(\x05R\x05index\x12+\n" +
	"\x11short_description\x18\a \x01(\tR\x10shortDescription\x12!\n" +
	"\fannual_price\x18\b \x01(\x05R\vannualPrice2\xff\x04\n" +
	"\x11VtuberPlanService\x12z\n" +
	"\rAddVtuberPlan\x12$.api.vtubers.v1.AddVtuberPlanRequest\x1a%.api.vtubers.v1.AddVtuberPlanResponse\"\x1c\x82\xb5\x18\x18\b\x01\"\x14_user.vtuberId!=null\x12|\n" +
	"\x1bGetAllVtuberPlansByVtuberId\x122.api.vtubers.v1.GetAllVtuberPlansByVtuberIdRequest\x1a).api.vtubers.v1.GetAllVtuberPlansResponse\x12h\n" +
	"\x11GetVtuberPlanById\x12(.api.vtubers.v1.GetVtuberPlanByIdRequest\x1a).api.vtubers.v1.GetVtuberPlanByIdResponse\x12\x81\x01\n" +
	"\x14DeleteVtuberPlanById\x12+.api.vtubers.v1.DeleteVtuberPlanByIdRequest\x1a\x1e.api.shared.v1.GenericResponse\"\x1c\x82\xb5\x18\x18\b\x01\"\x14_user.vtuberId!=null\x12\x81\x01\n" +
	"\x14UpdateVtuberPlanById\x12+.api.vtubers.v1.UpdateVtuberPlanByIdRequest\x1a\x1e.api.shared.v1.GenericResponse\"\x1c\x82\xb5\x18\x18\b\x01\"\x14_user.vtuberId!=nullB4Z2github.com/nsp-inc/vtuber/api/vtubers/v1;vtubersv1b\x06proto3"

var (
	file_vtubers_v1_vtuberplan_proto_rawDescOnce sync.Once
	file_vtubers_v1_vtuberplan_proto_rawDescData []byte
)

func file_vtubers_v1_vtuberplan_proto_rawDescGZIP() []byte {
	file_vtubers_v1_vtuberplan_proto_rawDescOnce.Do(func() {
		file_vtubers_v1_vtuberplan_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtuberplan_proto_rawDesc), len(file_vtubers_v1_vtuberplan_proto_rawDesc)))
	})
	return file_vtubers_v1_vtuberplan_proto_rawDescData
}

var file_vtubers_v1_vtuberplan_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_vtubers_v1_vtuberplan_proto_goTypes = []any{
	(*AddVtuberPlanRequest)(nil),               // 0: api.vtubers.v1.AddVtuberPlanRequest
	(*AddVtuberPlanResponse)(nil),              // 1: api.vtubers.v1.AddVtuberPlanResponse
	(*VtuberPlan)(nil),                         // 2: api.vtubers.v1.VtuberPlan
	(*GetAllVtuberPlansByVtuberIdRequest)(nil), // 3: api.vtubers.v1.GetAllVtuberPlansByVtuberIdRequest
	(*GetAllVtuberPlansResponse)(nil),          // 4: api.vtubers.v1.GetAllVtuberPlansResponse
	(*GetVtuberPlanByIdRequest)(nil),           // 5: api.vtubers.v1.GetVtuberPlanByIdRequest
	(*GetVtuberPlanByIdResponse)(nil),          // 6: api.vtubers.v1.GetVtuberPlanByIdResponse
	(*DeleteVtuberPlanByIdRequest)(nil),        // 7: api.vtubers.v1.DeleteVtuberPlanByIdRequest
	(*UpdateVtuberPlanByIdRequest)(nil),        // 8: api.vtubers.v1.UpdateVtuberPlanByIdRequest
	(*timestamppb.Timestamp)(nil),              // 9: google.protobuf.Timestamp
	(*v1.GenericResponse)(nil),                 // 10: api.shared.v1.GenericResponse
}
var file_vtubers_v1_vtuberplan_proto_depIdxs = []int32{
	2,  // 0: api.vtubers.v1.AddVtuberPlanResponse.data:type_name -> api.vtubers.v1.VtuberPlan
	9,  // 1: api.vtubers.v1.VtuberPlan.created_at:type_name -> google.protobuf.Timestamp
	2,  // 2: api.vtubers.v1.GetAllVtuberPlansResponse.VtuberPlan:type_name -> api.vtubers.v1.VtuberPlan
	2,  // 3: api.vtubers.v1.GetVtuberPlanByIdResponse.data:type_name -> api.vtubers.v1.VtuberPlan
	0,  // 4: api.vtubers.v1.VtuberPlanService.AddVtuberPlan:input_type -> api.vtubers.v1.AddVtuberPlanRequest
	3,  // 5: api.vtubers.v1.VtuberPlanService.GetAllVtuberPlansByVtuberId:input_type -> api.vtubers.v1.GetAllVtuberPlansByVtuberIdRequest
	5,  // 6: api.vtubers.v1.VtuberPlanService.GetVtuberPlanById:input_type -> api.vtubers.v1.GetVtuberPlanByIdRequest
	7,  // 7: api.vtubers.v1.VtuberPlanService.DeleteVtuberPlanById:input_type -> api.vtubers.v1.DeleteVtuberPlanByIdRequest
	8,  // 8: api.vtubers.v1.VtuberPlanService.UpdateVtuberPlanById:input_type -> api.vtubers.v1.UpdateVtuberPlanByIdRequest
	1,  // 9: api.vtubers.v1.VtuberPlanService.AddVtuberPlan:output_type -> api.vtubers.v1.AddVtuberPlanResponse
	4,  // 10: api.vtubers.v1.VtuberPlanService.GetAllVtuberPlansByVtuberId:output_type -> api.vtubers.v1.GetAllVtuberPlansResponse
	6,  // 11: api.vtubers.v1.VtuberPlanService.GetVtuberPlanById:output_type -> api.vtubers.v1.GetVtuberPlanByIdResponse
	10, // 12: api.vtubers.v1.VtuberPlanService.DeleteVtuberPlanById:output_type -> api.shared.v1.GenericResponse
	10, // 13: api.vtubers.v1.VtuberPlanService.UpdateVtuberPlanById:output_type -> api.shared.v1.GenericResponse
	9,  // [9:14] is the sub-list for method output_type
	4,  // [4:9] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_vtubers_v1_vtuberplan_proto_init() }
func file_vtubers_v1_vtuberplan_proto_init() {
	if File_vtubers_v1_vtuberplan_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtuberplan_proto_rawDesc), len(file_vtubers_v1_vtuberplan_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_vtubers_v1_vtuberplan_proto_goTypes,
		DependencyIndexes: file_vtubers_v1_vtuberplan_proto_depIdxs,
		MessageInfos:      file_vtubers_v1_vtuberplan_proto_msgTypes,
	}.Build()
	File_vtubers_v1_vtuberplan_proto = out.File
	file_vtubers_v1_vtuberplan_proto_goTypes = nil
	file_vtubers_v1_vtuberplan_proto_depIdxs = nil
}
