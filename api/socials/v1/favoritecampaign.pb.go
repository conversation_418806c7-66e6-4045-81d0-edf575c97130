// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: socials/v1/favoritecampaign.proto

package socialsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddFavoriteCampaignRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CampaignId    int64                  `protobuf:"varint,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddFavoriteCampaignRequest) Reset() {
	*x = AddFavoriteCampaignRequest{}
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFavoriteCampaignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFavoriteCampaignRequest) ProtoMessage() {}

func (x *AddFavoriteCampaignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFavoriteCampaignRequest.ProtoReflect.Descriptor instead.
func (*AddFavoriteCampaignRequest) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoritecampaign_proto_rawDescGZIP(), []int{0}
}

func (x *AddFavoriteCampaignRequest) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

type AddFavoriteCampaignResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *FavoriteCampaign      `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddFavoriteCampaignResponse) Reset() {
	*x = AddFavoriteCampaignResponse{}
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFavoriteCampaignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFavoriteCampaignResponse) ProtoMessage() {}

func (x *AddFavoriteCampaignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFavoriteCampaignResponse.ProtoReflect.Descriptor instead.
func (*AddFavoriteCampaignResponse) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoritecampaign_proto_rawDescGZIP(), []int{1}
}

func (x *AddFavoriteCampaignResponse) GetData() *FavoriteCampaign {
	if x != nil {
		return x.Data
	}
	return nil
}

type FavoriteCampaignWithDetails struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	CampaignId       int64                  `protobuf:"varint,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	UserId           int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Id               int64                  `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	ShortDescription string                 `protobuf:"bytes,5,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty"`
	Name             string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	Image            string                 `protobuf:"bytes,7,opt,name=image,proto3" json:"image,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *FavoriteCampaignWithDetails) Reset() {
	*x = FavoriteCampaignWithDetails{}
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FavoriteCampaignWithDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FavoriteCampaignWithDetails) ProtoMessage() {}

func (x *FavoriteCampaignWithDetails) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FavoriteCampaignWithDetails.ProtoReflect.Descriptor instead.
func (*FavoriteCampaignWithDetails) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoritecampaign_proto_rawDescGZIP(), []int{2}
}

func (x *FavoriteCampaignWithDetails) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

func (x *FavoriteCampaignWithDetails) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *FavoriteCampaignWithDetails) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FavoriteCampaignWithDetails) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FavoriteCampaignWithDetails) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *FavoriteCampaignWithDetails) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FavoriteCampaignWithDetails) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type FavoriteCampaign struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CampaignId    int64                  `protobuf:"varint,2,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	UserId        int64                  `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FavoriteCampaign) Reset() {
	*x = FavoriteCampaign{}
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FavoriteCampaign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FavoriteCampaign) ProtoMessage() {}

func (x *FavoriteCampaign) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FavoriteCampaign.ProtoReflect.Descriptor instead.
func (*FavoriteCampaign) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoritecampaign_proto_rawDescGZIP(), []int{3}
}

func (x *FavoriteCampaign) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FavoriteCampaign) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

func (x *FavoriteCampaign) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *FavoriteCampaign) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FavoriteCampaign) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type GetAllFavoriteCampaignRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,3,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllFavoriteCampaignRequest) Reset() {
	*x = GetAllFavoriteCampaignRequest{}
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllFavoriteCampaignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllFavoriteCampaignRequest) ProtoMessage() {}

func (x *GetAllFavoriteCampaignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllFavoriteCampaignRequest.ProtoReflect.Descriptor instead.
func (*GetAllFavoriteCampaignRequest) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoritecampaign_proto_rawDescGZIP(), []int{4}
}

func (x *GetAllFavoriteCampaignRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetAllFavoriteCampaignResponse struct {
	state             protoimpl.MessageState         `protogen:"open.v1"`
	Data              []*FavoriteCampaignWithDetails `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails          `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllFavoriteCampaignResponse) Reset() {
	*x = GetAllFavoriteCampaignResponse{}
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllFavoriteCampaignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllFavoriteCampaignResponse) ProtoMessage() {}

func (x *GetAllFavoriteCampaignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllFavoriteCampaignResponse.ProtoReflect.Descriptor instead.
func (*GetAllFavoriteCampaignResponse) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoritecampaign_proto_rawDescGZIP(), []int{5}
}

func (x *GetAllFavoriteCampaignResponse) GetData() []*FavoriteCampaignWithDetails {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllFavoriteCampaignResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type DeleteFavoriteCampaignRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CampaignId    int64                  `protobuf:"varint,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFavoriteCampaignRequest) Reset() {
	*x = DeleteFavoriteCampaignRequest{}
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFavoriteCampaignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFavoriteCampaignRequest) ProtoMessage() {}

func (x *DeleteFavoriteCampaignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoritecampaign_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFavoriteCampaignRequest.ProtoReflect.Descriptor instead.
func (*DeleteFavoriteCampaignRequest) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoritecampaign_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteFavoriteCampaignRequest) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

var File_socials_v1_favoritecampaign_proto protoreflect.FileDescriptor

const file_socials_v1_favoritecampaign_proto_rawDesc = "" +
	"\n" +
	"!socials/v1/favoritecampaign.proto\x12\x0eapi.socials.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"=\n" +
	"\x1aAddFavoriteCampaignRequest\x12\x1f\n" +
	"\vcampaign_id\x18\x01 \x01(\x03R\n" +
	"campaignId\"S\n" +
	"\x1bAddFavoriteCampaignResponse\x124\n" +
	"\x04data\x18\x01 \x01(\v2 .api.socials.v1.FavoriteCampaignR\x04data\"\xf9\x01\n" +
	"\x1bFavoriteCampaignWithDetails\x12\x1f\n" +
	"\vcampaign_id\x18\x01 \x01(\x03R\n" +
	"campaignId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\x129\n" +
	"\n" +
	"created_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x0e\n" +
	"\x02id\x18\x04 \x01(\x03R\x02id\x12+\n" +
	"\x11short_description\x18\x05 \x01(\tR\x10shortDescription\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12\x14\n" +
	"\x05image\x18\a \x01(\tR\x05image\"\xd2\x01\n" +
	"\x10FavoriteCampaign\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcampaign_id\x18\x02 \x01(\x03R\n" +
	"campaignId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\x03R\x06userId\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"u\n" +
	"\x1dGetAllFavoriteCampaignRequest\x12E\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\xb2\x01\n" +
	"\x1eGetAllFavoriteCampaignResponse\x12?\n" +
	"\x04data\x18\x01 \x03(\v2+.api.socials.v1.FavoriteCampaignWithDetailsR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"@\n" +
	"\x1dDeleteFavoriteCampaignRequest\x12\x1f\n" +
	"\vcampaign_id\x18\x01 \x01(\x03R\n" +
	"campaignId2\x83\x03\n" +
	"\x17FavoriteCampaignService\x12v\n" +
	"\x13AddFavoriteCampaign\x12*.api.socials.v1.AddFavoriteCampaignRequest\x1a+.api.socials.v1.AddFavoriteCampaignResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x7f\n" +
	"\x16GetAllFavoriteCampaign\x12-.api.socials.v1.GetAllFavoriteCampaignRequest\x1a..api.socials.v1.GetAllFavoriteCampaignResponse\"\x06\x82\xb5\x18\x02\b\x01\x12o\n" +
	"\x16DeleteFavoriteCampaign\x12-.api.socials.v1.DeleteFavoriteCampaignRequest\x1a\x1e.api.shared.v1.GenericResponse\"\x06\x82\xb5\x18\x02\b\x01B4Z2github.com/nsp-inc/vtuber/api/socials/v1;socialsv1b\x06proto3"

var (
	file_socials_v1_favoritecampaign_proto_rawDescOnce sync.Once
	file_socials_v1_favoritecampaign_proto_rawDescData []byte
)

func file_socials_v1_favoritecampaign_proto_rawDescGZIP() []byte {
	file_socials_v1_favoritecampaign_proto_rawDescOnce.Do(func() {
		file_socials_v1_favoritecampaign_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_socials_v1_favoritecampaign_proto_rawDesc), len(file_socials_v1_favoritecampaign_proto_rawDesc)))
	})
	return file_socials_v1_favoritecampaign_proto_rawDescData
}

var file_socials_v1_favoritecampaign_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_socials_v1_favoritecampaign_proto_goTypes = []any{
	(*AddFavoriteCampaignRequest)(nil),     // 0: api.socials.v1.AddFavoriteCampaignRequest
	(*AddFavoriteCampaignResponse)(nil),    // 1: api.socials.v1.AddFavoriteCampaignResponse
	(*FavoriteCampaignWithDetails)(nil),    // 2: api.socials.v1.FavoriteCampaignWithDetails
	(*FavoriteCampaign)(nil),               // 3: api.socials.v1.FavoriteCampaign
	(*GetAllFavoriteCampaignRequest)(nil),  // 4: api.socials.v1.GetAllFavoriteCampaignRequest
	(*GetAllFavoriteCampaignResponse)(nil), // 5: api.socials.v1.GetAllFavoriteCampaignResponse
	(*DeleteFavoriteCampaignRequest)(nil),  // 6: api.socials.v1.DeleteFavoriteCampaignRequest
	(*timestamppb.Timestamp)(nil),          // 7: google.protobuf.Timestamp
	(*v1.PaginationRequest)(nil),           // 8: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),           // 9: api.shared.v1.PaginationDetails
	(*v1.GenericResponse)(nil),             // 10: api.shared.v1.GenericResponse
}
var file_socials_v1_favoritecampaign_proto_depIdxs = []int32{
	3,  // 0: api.socials.v1.AddFavoriteCampaignResponse.data:type_name -> api.socials.v1.FavoriteCampaign
	7,  // 1: api.socials.v1.FavoriteCampaignWithDetails.created_at:type_name -> google.protobuf.Timestamp
	7,  // 2: api.socials.v1.FavoriteCampaign.created_at:type_name -> google.protobuf.Timestamp
	7,  // 3: api.socials.v1.FavoriteCampaign.updated_at:type_name -> google.protobuf.Timestamp
	8,  // 4: api.socials.v1.GetAllFavoriteCampaignRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 5: api.socials.v1.GetAllFavoriteCampaignResponse.data:type_name -> api.socials.v1.FavoriteCampaignWithDetails
	9,  // 6: api.socials.v1.GetAllFavoriteCampaignResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	0,  // 7: api.socials.v1.FavoriteCampaignService.AddFavoriteCampaign:input_type -> api.socials.v1.AddFavoriteCampaignRequest
	4,  // 8: api.socials.v1.FavoriteCampaignService.GetAllFavoriteCampaign:input_type -> api.socials.v1.GetAllFavoriteCampaignRequest
	6,  // 9: api.socials.v1.FavoriteCampaignService.DeleteFavoriteCampaign:input_type -> api.socials.v1.DeleteFavoriteCampaignRequest
	1,  // 10: api.socials.v1.FavoriteCampaignService.AddFavoriteCampaign:output_type -> api.socials.v1.AddFavoriteCampaignResponse
	5,  // 11: api.socials.v1.FavoriteCampaignService.GetAllFavoriteCampaign:output_type -> api.socials.v1.GetAllFavoriteCampaignResponse
	10, // 12: api.socials.v1.FavoriteCampaignService.DeleteFavoriteCampaign:output_type -> api.shared.v1.GenericResponse
	10, // [10:13] is the sub-list for method output_type
	7,  // [7:10] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_socials_v1_favoritecampaign_proto_init() }
func file_socials_v1_favoritecampaign_proto_init() {
	if File_socials_v1_favoritecampaign_proto != nil {
		return
	}
	file_socials_v1_favoritecampaign_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_socials_v1_favoritecampaign_proto_rawDesc), len(file_socials_v1_favoritecampaign_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_socials_v1_favoritecampaign_proto_goTypes,
		DependencyIndexes: file_socials_v1_favoritecampaign_proto_depIdxs,
		MessageInfos:      file_socials_v1_favoritecampaign_proto_msgTypes,
	}.Build()
	File_socials_v1_favoritecampaign_proto = out.File
	file_socials_v1_favoritecampaign_proto_goTypes = nil
	file_socials_v1_favoritecampaign_proto_depIdxs = nil
}
