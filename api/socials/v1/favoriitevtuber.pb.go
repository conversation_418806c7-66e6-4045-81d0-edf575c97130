// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: socials/v1/favoriitevtuber.proto

package socialsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddFavoriteVtuberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VtuberId      int64                  `protobuf:"varint,1,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddFavoriteVtuberRequest) Reset() {
	*x = AddFavoriteVtuberRequest{}
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFavoriteVtuberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFavoriteVtuberRequest) ProtoMessage() {}

func (x *AddFavoriteVtuberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFavoriteVtuberRequest.ProtoReflect.Descriptor instead.
func (*AddFavoriteVtuberRequest) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoriitevtuber_proto_rawDescGZIP(), []int{0}
}

func (x *AddFavoriteVtuberRequest) GetVtuberId() int64 {
	if x != nil {
		return x.VtuberId
	}
	return 0
}

type AddFavoriteVtuberResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *FavoriteVtuber        `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddFavoriteVtuberResponse) Reset() {
	*x = AddFavoriteVtuberResponse{}
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFavoriteVtuberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFavoriteVtuberResponse) ProtoMessage() {}

func (x *AddFavoriteVtuberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFavoriteVtuberResponse.ProtoReflect.Descriptor instead.
func (*AddFavoriteVtuberResponse) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoriitevtuber_proto_rawDescGZIP(), []int{1}
}

func (x *AddFavoriteVtuberResponse) GetData() *FavoriteVtuber {
	if x != nil {
		return x.Data
	}
	return nil
}

type FavoriteVtuberWithDetails struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Vtuber        *v1.VtuberProfile      `protobuf:"bytes,1,opt,name=vtuber,proto3" json:"vtuber,omitempty"`
	VtuberId      int64                  `protobuf:"varint,2,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty"`
	UserId        int64                  `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Id            int64                  `protobuf:"varint,6,opt,name=id,proto3" json:"id,omitempty"`
	Username      string                 `protobuf:"bytes,7,opt,name=username,proto3" json:"username,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FavoriteVtuberWithDetails) Reset() {
	*x = FavoriteVtuberWithDetails{}
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FavoriteVtuberWithDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FavoriteVtuberWithDetails) ProtoMessage() {}

func (x *FavoriteVtuberWithDetails) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FavoriteVtuberWithDetails.ProtoReflect.Descriptor instead.
func (*FavoriteVtuberWithDetails) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoriitevtuber_proto_rawDescGZIP(), []int{2}
}

func (x *FavoriteVtuberWithDetails) GetVtuber() *v1.VtuberProfile {
	if x != nil {
		return x.Vtuber
	}
	return nil
}

func (x *FavoriteVtuberWithDetails) GetVtuberId() int64 {
	if x != nil {
		return x.VtuberId
	}
	return 0
}

func (x *FavoriteVtuberWithDetails) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *FavoriteVtuberWithDetails) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FavoriteVtuberWithDetails) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *FavoriteVtuberWithDetails) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FavoriteVtuberWithDetails) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type FavoriteVtuber struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	VtuberId      int64                  `protobuf:"varint,2,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty"`
	UserId        int64                  `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Username      string                 `protobuf:"bytes,6,opt,name=username,proto3" json:"username,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FavoriteVtuber) Reset() {
	*x = FavoriteVtuber{}
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FavoriteVtuber) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FavoriteVtuber) ProtoMessage() {}

func (x *FavoriteVtuber) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FavoriteVtuber.ProtoReflect.Descriptor instead.
func (*FavoriteVtuber) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoriitevtuber_proto_rawDescGZIP(), []int{3}
}

func (x *FavoriteVtuber) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FavoriteVtuber) GetVtuberId() int64 {
	if x != nil {
		return x.VtuberId
	}
	return 0
}

func (x *FavoriteVtuber) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *FavoriteVtuber) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FavoriteVtuber) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *FavoriteVtuber) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type GetAllFavoriteVtuberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllFavoriteVtuberRequest) Reset() {
	*x = GetAllFavoriteVtuberRequest{}
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllFavoriteVtuberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllFavoriteVtuberRequest) ProtoMessage() {}

func (x *GetAllFavoriteVtuberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllFavoriteVtuberRequest.ProtoReflect.Descriptor instead.
func (*GetAllFavoriteVtuberRequest) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoriitevtuber_proto_rawDescGZIP(), []int{4}
}

func (x *GetAllFavoriteVtuberRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetAllFavoriteVtuberResponse struct {
	state             protoimpl.MessageState       `protogen:"open.v1"`
	Data              []*FavoriteVtuberWithDetails `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v11.PaginationDetails       `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllFavoriteVtuberResponse) Reset() {
	*x = GetAllFavoriteVtuberResponse{}
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllFavoriteVtuberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllFavoriteVtuberResponse) ProtoMessage() {}

func (x *GetAllFavoriteVtuberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllFavoriteVtuberResponse.ProtoReflect.Descriptor instead.
func (*GetAllFavoriteVtuberResponse) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoriitevtuber_proto_rawDescGZIP(), []int{5}
}

func (x *GetAllFavoriteVtuberResponse) GetData() []*FavoriteVtuberWithDetails {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllFavoriteVtuberResponse) GetPaginationDetails() *v11.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type DeleteFavoriteVtuberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VtuberId      int64                  `protobuf:"varint,1,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFavoriteVtuberRequest) Reset() {
	*x = DeleteFavoriteVtuberRequest{}
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFavoriteVtuberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFavoriteVtuberRequest) ProtoMessage() {}

func (x *DeleteFavoriteVtuberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_socials_v1_favoriitevtuber_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFavoriteVtuberRequest.ProtoReflect.Descriptor instead.
func (*DeleteFavoriteVtuberRequest) Descriptor() ([]byte, []int) {
	return file_socials_v1_favoriitevtuber_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteFavoriteVtuberRequest) GetVtuberId() int64 {
	if x != nil {
		return x.VtuberId
	}
	return 0
}

var File_socials_v1_favoriitevtuber_proto protoreflect.FileDescriptor

const file_socials_v1_favoriitevtuber_proto_rawDesc = "" +
	"\n" +
	" socials/v1/favoriitevtuber.proto\x12\x0eapi.socials.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\x1a\x1fvtubers/v1/vtuberprofiles.proto\"7\n" +
	"\x18AddFavoriteVtuberRequest\x12\x1b\n" +
	"\tvtuber_id\x18\x01 \x01(\x03R\bvtuberId\"O\n" +
	"\x19AddFavoriteVtuberResponse\x122\n" +
	"\x04data\x18\x01 \x01(\v2\x1e.api.socials.v1.FavoriteVtuberR\x04data\"\xaa\x02\n" +
	"\x19FavoriteVtuberWithDetails\x125\n" +
	"\x06vtuber\x18\x01 \x01(\v2\x1d.api.vtubers.v1.VtuberProfileR\x06vtuber\x12\x1b\n" +
	"\tvtuber_id\x18\x02 \x01(\x03R\bvtuberId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\x03R\x06userId\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x0e\n" +
	"\x02id\x18\x06 \x01(\x03R\x02id\x12\x1a\n" +
	"\busername\x18\a \x01(\tR\busername\"\xe8\x01\n" +
	"\x0eFavoriteVtuber\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tvtuber_id\x18\x02 \x01(\x03R\bvtuberId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\x03R\x06userId\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x1a\n" +
	"\busername\x18\x06 \x01(\tR\busername\"s\n" +
	"\x1bGetAllFavoriteVtuberRequest\x12E\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\xae\x01\n" +
	"\x1cGetAllFavoriteVtuberResponse\x12=\n" +
	"\x04data\x18\x01 \x03(\v2).api.socials.v1.FavoriteVtuberWithDetailsR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\":\n" +
	"\x1bDeleteFavoriteVtuberRequest\x12\x1b\n" +
	"\tvtuber_id\x18\x01 \x01(\x03R\bvtuberId2\xf1\x02\n" +
	"\x15FavoriteVtuberService\x12p\n" +
	"\x11AddFavoriteVtuber\x12(.api.socials.v1.AddFavoriteVtuberRequest\x1a).api.socials.v1.AddFavoriteVtuberResponse\"\x06\x82\xb5\x18\x02\b\x01\x12y\n" +
	"\x14GetAllFavoriteVtuber\x12+.api.socials.v1.GetAllFavoriteVtuberRequest\x1a,.api.socials.v1.GetAllFavoriteVtuberResponse\"\x06\x82\xb5\x18\x02\b\x01\x12k\n" +
	"\x14DeleteFavoriteVtuber\x12+.api.socials.v1.DeleteFavoriteVtuberRequest\x1a\x1e.api.shared.v1.GenericResponse\"\x06\x82\xb5\x18\x02\b\x01B4Z2github.com/nsp-inc/vtuber/api/socials/v1;socialsv1b\x06proto3"

var (
	file_socials_v1_favoriitevtuber_proto_rawDescOnce sync.Once
	file_socials_v1_favoriitevtuber_proto_rawDescData []byte
)

func file_socials_v1_favoriitevtuber_proto_rawDescGZIP() []byte {
	file_socials_v1_favoriitevtuber_proto_rawDescOnce.Do(func() {
		file_socials_v1_favoriitevtuber_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_socials_v1_favoriitevtuber_proto_rawDesc), len(file_socials_v1_favoriitevtuber_proto_rawDesc)))
	})
	return file_socials_v1_favoriitevtuber_proto_rawDescData
}

var file_socials_v1_favoriitevtuber_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_socials_v1_favoriitevtuber_proto_goTypes = []any{
	(*AddFavoriteVtuberRequest)(nil),     // 0: api.socials.v1.AddFavoriteVtuberRequest
	(*AddFavoriteVtuberResponse)(nil),    // 1: api.socials.v1.AddFavoriteVtuberResponse
	(*FavoriteVtuberWithDetails)(nil),    // 2: api.socials.v1.FavoriteVtuberWithDetails
	(*FavoriteVtuber)(nil),               // 3: api.socials.v1.FavoriteVtuber
	(*GetAllFavoriteVtuberRequest)(nil),  // 4: api.socials.v1.GetAllFavoriteVtuberRequest
	(*GetAllFavoriteVtuberResponse)(nil), // 5: api.socials.v1.GetAllFavoriteVtuberResponse
	(*DeleteFavoriteVtuberRequest)(nil),  // 6: api.socials.v1.DeleteFavoriteVtuberRequest
	(*v1.VtuberProfile)(nil),             // 7: api.vtubers.v1.VtuberProfile
	(*timestamppb.Timestamp)(nil),        // 8: google.protobuf.Timestamp
	(*v11.PaginationRequest)(nil),        // 9: api.shared.v1.PaginationRequest
	(*v11.PaginationDetails)(nil),        // 10: api.shared.v1.PaginationDetails
	(*v11.GenericResponse)(nil),          // 11: api.shared.v1.GenericResponse
}
var file_socials_v1_favoriitevtuber_proto_depIdxs = []int32{
	3,  // 0: api.socials.v1.AddFavoriteVtuberResponse.data:type_name -> api.socials.v1.FavoriteVtuber
	7,  // 1: api.socials.v1.FavoriteVtuberWithDetails.vtuber:type_name -> api.vtubers.v1.VtuberProfile
	8,  // 2: api.socials.v1.FavoriteVtuberWithDetails.created_at:type_name -> google.protobuf.Timestamp
	8,  // 3: api.socials.v1.FavoriteVtuberWithDetails.updated_at:type_name -> google.protobuf.Timestamp
	8,  // 4: api.socials.v1.FavoriteVtuber.created_at:type_name -> google.protobuf.Timestamp
	8,  // 5: api.socials.v1.FavoriteVtuber.updated_at:type_name -> google.protobuf.Timestamp
	9,  // 6: api.socials.v1.GetAllFavoriteVtuberRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 7: api.socials.v1.GetAllFavoriteVtuberResponse.data:type_name -> api.socials.v1.FavoriteVtuberWithDetails
	10, // 8: api.socials.v1.GetAllFavoriteVtuberResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	0,  // 9: api.socials.v1.FavoriteVtuberService.AddFavoriteVtuber:input_type -> api.socials.v1.AddFavoriteVtuberRequest
	4,  // 10: api.socials.v1.FavoriteVtuberService.GetAllFavoriteVtuber:input_type -> api.socials.v1.GetAllFavoriteVtuberRequest
	6,  // 11: api.socials.v1.FavoriteVtuberService.DeleteFavoriteVtuber:input_type -> api.socials.v1.DeleteFavoriteVtuberRequest
	1,  // 12: api.socials.v1.FavoriteVtuberService.AddFavoriteVtuber:output_type -> api.socials.v1.AddFavoriteVtuberResponse
	5,  // 13: api.socials.v1.FavoriteVtuberService.GetAllFavoriteVtuber:output_type -> api.socials.v1.GetAllFavoriteVtuberResponse
	11, // 14: api.socials.v1.FavoriteVtuberService.DeleteFavoriteVtuber:output_type -> api.shared.v1.GenericResponse
	12, // [12:15] is the sub-list for method output_type
	9,  // [9:12] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_socials_v1_favoriitevtuber_proto_init() }
func file_socials_v1_favoriitevtuber_proto_init() {
	if File_socials_v1_favoriitevtuber_proto != nil {
		return
	}
	file_socials_v1_favoriitevtuber_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_socials_v1_favoriitevtuber_proto_rawDesc), len(file_socials_v1_favoriitevtuber_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_socials_v1_favoriitevtuber_proto_goTypes,
		DependencyIndexes: file_socials_v1_favoriitevtuber_proto_depIdxs,
		MessageInfos:      file_socials_v1_favoriitevtuber_proto_msgTypes,
	}.Build()
	File_socials_v1_favoriitevtuber_proto = out.File
	file_socials_v1_favoriitevtuber_proto_goTypes = nil
	file_socials_v1_favoriitevtuber_proto_depIdxs = nil
}
