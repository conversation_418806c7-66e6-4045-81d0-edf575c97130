// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: campaigns/v1/campaignbanner.proto

package campaignsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddCampaignBannerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Image         string                 `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty" validate:"required"`                               
	CampaignId    int64                  `protobuf:"varint,2,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty" validate:"required"`  
	Index         int32                  `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty" validate:"required"`                              
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCampaignBannerRequest) Reset() {
	*x = AddCampaignBannerRequest{}
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCampaignBannerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCampaignBannerRequest) ProtoMessage() {}

func (x *AddCampaignBannerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCampaignBannerRequest.ProtoReflect.Descriptor instead.
func (*AddCampaignBannerRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignbanner_proto_rawDescGZIP(), []int{0}
}

func (x *AddCampaignBannerRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *AddCampaignBannerRequest) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

func (x *AddCampaignBannerRequest) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type AddCampaignBannerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *CampaignBanner        `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCampaignBannerResponse) Reset() {
	*x = AddCampaignBannerResponse{}
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCampaignBannerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCampaignBannerResponse) ProtoMessage() {}

func (x *AddCampaignBannerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCampaignBannerResponse.ProtoReflect.Descriptor instead.
func (*AddCampaignBannerResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignbanner_proto_rawDescGZIP(), []int{1}
}

func (x *AddCampaignBannerResponse) GetData() *CampaignBanner {
	if x != nil {
		return x.Data
	}
	return nil
}

type CampaignBanner struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Image         string                 `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
	Index         int32                  `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`
	CampaignId    int64                  `protobuf:"varint,4,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CampaignBanner) Reset() {
	*x = CampaignBanner{}
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CampaignBanner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignBanner) ProtoMessage() {}

func (x *CampaignBanner) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignBanner.ProtoReflect.Descriptor instead.
func (*CampaignBanner) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignbanner_proto_rawDescGZIP(), []int{2}
}

func (x *CampaignBanner) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CampaignBanner) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *CampaignBanner) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *CampaignBanner) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

func (x *CampaignBanner) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type GetCampaignBannerByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignBannerByIdRequest) Reset() {
	*x = GetCampaignBannerByIdRequest{}
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignBannerByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignBannerByIdRequest) ProtoMessage() {}

func (x *GetCampaignBannerByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignBannerByIdRequest.ProtoReflect.Descriptor instead.
func (*GetCampaignBannerByIdRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignbanner_proto_rawDescGZIP(), []int{3}
}

func (x *GetCampaignBannerByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetCampaignBannerByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *CampaignBanner        `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCampaignBannerByIdResponse) Reset() {
	*x = GetCampaignBannerByIdResponse{}
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCampaignBannerByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignBannerByIdResponse) ProtoMessage() {}

func (x *GetCampaignBannerByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignBannerByIdResponse.ProtoReflect.Descriptor instead.
func (*GetCampaignBannerByIdResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignbanner_proto_rawDescGZIP(), []int{4}
}

func (x *GetCampaignBannerByIdResponse) GetData() *CampaignBanner {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteCampaignBannerByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCampaignBannerByIdRequest) Reset() {
	*x = DeleteCampaignBannerByIdRequest{}
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCampaignBannerByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCampaignBannerByIdRequest) ProtoMessage() {}

func (x *DeleteCampaignBannerByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCampaignBannerByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteCampaignBannerByIdRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignbanner_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteCampaignBannerByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateCampaignBannerByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Image         string                 `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	Id            int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty" validate:"required"`        
	Index         int32                  `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCampaignBannerByIdRequest) Reset() {
	*x = UpdateCampaignBannerByIdRequest{}
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCampaignBannerByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCampaignBannerByIdRequest) ProtoMessage() {}

func (x *UpdateCampaignBannerByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCampaignBannerByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdateCampaignBannerByIdRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignbanner_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateCampaignBannerByIdRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UpdateCampaignBannerByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCampaignBannerByIdRequest) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type GetBannerByCampaignIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CampaignId    int64                  `protobuf:"varint,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBannerByCampaignIdRequest) Reset() {
	*x = GetBannerByCampaignIdRequest{}
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBannerByCampaignIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBannerByCampaignIdRequest) ProtoMessage() {}

func (x *GetBannerByCampaignIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBannerByCampaignIdRequest.ProtoReflect.Descriptor instead.
func (*GetBannerByCampaignIdRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignbanner_proto_rawDescGZIP(), []int{7}
}

func (x *GetBannerByCampaignIdRequest) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

type GetBannerByCampaignIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*CampaignBanner      `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBannerByCampaignIdResponse) Reset() {
	*x = GetBannerByCampaignIdResponse{}
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBannerByCampaignIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBannerByCampaignIdResponse) ProtoMessage() {}

func (x *GetBannerByCampaignIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBannerByCampaignIdResponse.ProtoReflect.Descriptor instead.
func (*GetBannerByCampaignIdResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignbanner_proto_rawDescGZIP(), []int{8}
}

func (x *GetBannerByCampaignIdResponse) GetData() []*CampaignBanner {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateCampaignBannerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCampaignBannerResponse) Reset() {
	*x = UpdateCampaignBannerResponse{}
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCampaignBannerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCampaignBannerResponse) ProtoMessage() {}

func (x *UpdateCampaignBannerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCampaignBannerResponse.ProtoReflect.Descriptor instead.
func (*UpdateCampaignBannerResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignbanner_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateCampaignBannerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateCampaignBannerResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteCampaignBannerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCampaignBannerResponse) Reset() {
	*x = DeleteCampaignBannerResponse{}
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCampaignBannerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCampaignBannerResponse) ProtoMessage() {}

func (x *DeleteCampaignBannerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaignbanner_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCampaignBannerResponse.ProtoReflect.Descriptor instead.
func (*DeleteCampaignBannerResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaignbanner_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteCampaignBannerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteCampaignBannerResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_campaigns_v1_campaignbanner_proto protoreflect.FileDescriptor

const file_campaigns_v1_campaignbanner_proto_rawDesc = "" +
	"\n" +
	"!campaigns/v1/campaignbanner.proto\x12\x10api.campaigns.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"g\n" +
	"\x18AddCampaignBannerRequest\x12\x14\n" +
	"\x05image\x18\x01 \x01(\tR\x05image\x12\x1f\n" +
	"\vcampaign_id\x18\x02 \x01(\x03R\n" +
	"campaignId\x12\x14\n" +
	"\x05index\x18\x03 \x01(\x05R\x05index\"Q\n" +
	"\x19AddCampaignBannerResponse\x124\n" +
	"\x04data\x18\x01 \x01(\v2 .api.campaigns.v1.CampaignBannerR\x04data\"\xa8\x01\n" +
	"\x0eCampaignBanner\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05image\x18\x02 \x01(\tR\x05image\x12\x14\n" +
	"\x05index\x18\x03 \x01(\x05R\x05index\x12\x1f\n" +
	"\vcampaign_id\x18\x04 \x01(\x03R\n" +
	"campaignId\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\".\n" +
	"\x1cGetCampaignBannerByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"U\n" +
	"\x1dGetCampaignBannerByIdResponse\x124\n" +
	"\x04data\x18\x01 \x01(\v2 .api.campaigns.v1.CampaignBannerR\x04data\"1\n" +
	"\x1fDeleteCampaignBannerByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"]\n" +
	"\x1fUpdateCampaignBannerByIdRequest\x12\x14\n" +
	"\x05image\x18\x01 \x01(\tR\x05image\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\x12\x14\n" +
	"\x05index\x18\x03 \x01(\x05R\x05index\"?\n" +
	"\x1cGetBannerByCampaignIdRequest\x12\x1f\n" +
	"\vcampaign_id\x18\x01 \x01(\x03R\n" +
	"campaignId\"U\n" +
	"\x1dGetBannerByCampaignIdResponse\x124\n" +
	"\x04data\x18\x01 \x03(\v2 .api.campaigns.v1.CampaignBannerR\x04data\"R\n" +
	"\x1cUpdateCampaignBannerResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"R\n" +
	"\x1cDeleteCampaignBannerResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\x93\x05\n" +
	"\x15CampaignBannerService\x12v\n" +
	"\x11AddCampaignBanner\x12*.api.campaigns.v1.AddCampaignBannerRequest\x1a+.api.campaigns.v1.AddCampaignBannerResponse\"\b\x82\xb5\x18\x04\b\x01\x18\x01\x12x\n" +
	"\x15GetCampaignBannerById\x12..api.campaigns.v1.GetCampaignBannerByIdRequest\x1a/.api.campaigns.v1.GetCampaignBannerByIdResponse\x12\x85\x01\n" +
	"\x18DeleteCampaignBannerById\x121.api.campaigns.v1.DeleteCampaignBannerByIdRequest\x1a..api.campaigns.v1.DeleteCampaignBannerResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x85\x01\n" +
	"\x18UpdateCampaignBannerById\x121.api.campaigns.v1.UpdateCampaignBannerByIdRequest\x1a..api.campaigns.v1.UpdateCampaignBannerResponse\"\x06\x82\xb5\x18\x02\b\x01\x12x\n" +
	"\x15GetBannerByCampaignId\x12..api.campaigns.v1.GetBannerByCampaignIdRequest\x1a/.api.campaigns.v1.GetBannerByCampaignIdResponseB8Z6github.com/nsp-inc/vtuber/api/campaigns/v1;campaignsv1b\x06proto3"

var (
	file_campaigns_v1_campaignbanner_proto_rawDescOnce sync.Once
	file_campaigns_v1_campaignbanner_proto_rawDescData []byte
)

func file_campaigns_v1_campaignbanner_proto_rawDescGZIP() []byte {
	file_campaigns_v1_campaignbanner_proto_rawDescOnce.Do(func() {
		file_campaigns_v1_campaignbanner_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_campaigns_v1_campaignbanner_proto_rawDesc), len(file_campaigns_v1_campaignbanner_proto_rawDesc)))
	})
	return file_campaigns_v1_campaignbanner_proto_rawDescData
}

var file_campaigns_v1_campaignbanner_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_campaigns_v1_campaignbanner_proto_goTypes = []any{
	(*AddCampaignBannerRequest)(nil),        // 0: api.campaigns.v1.AddCampaignBannerRequest
	(*AddCampaignBannerResponse)(nil),       // 1: api.campaigns.v1.AddCampaignBannerResponse
	(*CampaignBanner)(nil),                  // 2: api.campaigns.v1.CampaignBanner
	(*GetCampaignBannerByIdRequest)(nil),    // 3: api.campaigns.v1.GetCampaignBannerByIdRequest
	(*GetCampaignBannerByIdResponse)(nil),   // 4: api.campaigns.v1.GetCampaignBannerByIdResponse
	(*DeleteCampaignBannerByIdRequest)(nil), // 5: api.campaigns.v1.DeleteCampaignBannerByIdRequest
	(*UpdateCampaignBannerByIdRequest)(nil), // 6: api.campaigns.v1.UpdateCampaignBannerByIdRequest
	(*GetBannerByCampaignIdRequest)(nil),    // 7: api.campaigns.v1.GetBannerByCampaignIdRequest
	(*GetBannerByCampaignIdResponse)(nil),   // 8: api.campaigns.v1.GetBannerByCampaignIdResponse
	(*UpdateCampaignBannerResponse)(nil),    // 9: api.campaigns.v1.UpdateCampaignBannerResponse
	(*DeleteCampaignBannerResponse)(nil),    // 10: api.campaigns.v1.DeleteCampaignBannerResponse
	(*timestamppb.Timestamp)(nil),           // 11: google.protobuf.Timestamp
}
var file_campaigns_v1_campaignbanner_proto_depIdxs = []int32{
	2,  // 0: api.campaigns.v1.AddCampaignBannerResponse.data:type_name -> api.campaigns.v1.CampaignBanner
	11, // 1: api.campaigns.v1.CampaignBanner.created_at:type_name -> google.protobuf.Timestamp
	2,  // 2: api.campaigns.v1.GetCampaignBannerByIdResponse.data:type_name -> api.campaigns.v1.CampaignBanner
	2,  // 3: api.campaigns.v1.GetBannerByCampaignIdResponse.data:type_name -> api.campaigns.v1.CampaignBanner
	0,  // 4: api.campaigns.v1.CampaignBannerService.AddCampaignBanner:input_type -> api.campaigns.v1.AddCampaignBannerRequest
	3,  // 5: api.campaigns.v1.CampaignBannerService.GetCampaignBannerById:input_type -> api.campaigns.v1.GetCampaignBannerByIdRequest
	5,  // 6: api.campaigns.v1.CampaignBannerService.DeleteCampaignBannerById:input_type -> api.campaigns.v1.DeleteCampaignBannerByIdRequest
	6,  // 7: api.campaigns.v1.CampaignBannerService.UpdateCampaignBannerById:input_type -> api.campaigns.v1.UpdateCampaignBannerByIdRequest
	7,  // 8: api.campaigns.v1.CampaignBannerService.GetBannerByCampaignId:input_type -> api.campaigns.v1.GetBannerByCampaignIdRequest
	1,  // 9: api.campaigns.v1.CampaignBannerService.AddCampaignBanner:output_type -> api.campaigns.v1.AddCampaignBannerResponse
	4,  // 10: api.campaigns.v1.CampaignBannerService.GetCampaignBannerById:output_type -> api.campaigns.v1.GetCampaignBannerByIdResponse
	10, // 11: api.campaigns.v1.CampaignBannerService.DeleteCampaignBannerById:output_type -> api.campaigns.v1.DeleteCampaignBannerResponse
	9,  // 12: api.campaigns.v1.CampaignBannerService.UpdateCampaignBannerById:output_type -> api.campaigns.v1.UpdateCampaignBannerResponse
	8,  // 13: api.campaigns.v1.CampaignBannerService.GetBannerByCampaignId:output_type -> api.campaigns.v1.GetBannerByCampaignIdResponse
	9,  // [9:14] is the sub-list for method output_type
	4,  // [4:9] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_campaigns_v1_campaignbanner_proto_init() }
func file_campaigns_v1_campaignbanner_proto_init() {
	if File_campaigns_v1_campaignbanner_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_campaigns_v1_campaignbanner_proto_rawDesc), len(file_campaigns_v1_campaignbanner_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_campaigns_v1_campaignbanner_proto_goTypes,
		DependencyIndexes: file_campaigns_v1_campaignbanner_proto_depIdxs,
		MessageInfos:      file_campaigns_v1_campaignbanner_proto_msgTypes,
	}.Build()
	File_campaigns_v1_campaignbanner_proto = out.File
	file_campaigns_v1_campaignbanner_proto_goTypes = nil
	file_campaigns_v1_campaignbanner_proto_depIdxs = nil
}
