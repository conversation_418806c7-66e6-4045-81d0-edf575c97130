// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: content/v1/postlikes.proto

package contentv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddPostLikeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PostId        int64                  `protobuf:"varint,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddPostLikeRequest) Reset() {
	*x = AddPostLikeRequest{}
	mi := &file_content_v1_postlikes_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPostLikeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPostLikeRequest) ProtoMessage() {}

func (x *AddPostLikeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postlikes_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPostLikeRequest.ProtoReflect.Descriptor instead.
func (*AddPostLikeRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_postlikes_proto_rawDescGZIP(), []int{0}
}

func (x *AddPostLikeRequest) GetPostId() int64 {
	if x != nil {
		return x.PostId
	}
	return 0
}

type GetPostLikeCountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PostId        int64                  `protobuf:"varint,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPostLikeCountRequest) Reset() {
	*x = GetPostLikeCountRequest{}
	mi := &file_content_v1_postlikes_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPostLikeCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostLikeCountRequest) ProtoMessage() {}

func (x *GetPostLikeCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postlikes_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostLikeCountRequest.ProtoReflect.Descriptor instead.
func (*GetPostLikeCountRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_postlikes_proto_rawDescGZIP(), []int{1}
}

func (x *GetPostLikeCountRequest) GetPostId() int64 {
	if x != nil {
		return x.PostId
	}
	return 0
}

type GetAllPostLikeByUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllPostLikeByUserRequest) Reset() {
	*x = GetAllPostLikeByUserRequest{}
	mi := &file_content_v1_postlikes_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllPostLikeByUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllPostLikeByUserRequest) ProtoMessage() {}

func (x *GetAllPostLikeByUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postlikes_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllPostLikeByUserRequest.ProtoReflect.Descriptor instead.
func (*GetAllPostLikeByUserRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_postlikes_proto_rawDescGZIP(), []int{2}
}

func (x *GetAllPostLikeByUserRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetAllPostLikeByUserResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*PostLike            `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllPostLikeByUserResponse) Reset() {
	*x = GetAllPostLikeByUserResponse{}
	mi := &file_content_v1_postlikes_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllPostLikeByUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllPostLikeByUserResponse) ProtoMessage() {}

func (x *GetAllPostLikeByUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postlikes_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllPostLikeByUserResponse.ProtoReflect.Descriptor instead.
func (*GetAllPostLikeByUserResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_postlikes_proto_rawDescGZIP(), []int{3}
}

func (x *GetAllPostLikeByUserResponse) GetData() []*PostLike {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllPostLikeByUserResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type PostLike struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostId        int64                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PostLike) Reset() {
	*x = PostLike{}
	mi := &file_content_v1_postlikes_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostLike) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostLike) ProtoMessage() {}

func (x *PostLike) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postlikes_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostLike.ProtoReflect.Descriptor instead.
func (*PostLike) Descriptor() ([]byte, []int) {
	return file_content_v1_postlikes_proto_rawDescGZIP(), []int{4}
}

func (x *PostLike) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PostLike) GetPostId() int64 {
	if x != nil {
		return x.PostId
	}
	return 0
}

func (x *PostLike) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type GetPostLikeCountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         int64                  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPostLikeCountResponse) Reset() {
	*x = GetPostLikeCountResponse{}
	mi := &file_content_v1_postlikes_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPostLikeCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostLikeCountResponse) ProtoMessage() {}

func (x *GetPostLikeCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postlikes_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostLikeCountResponse.ProtoReflect.Descriptor instead.
func (*GetPostLikeCountResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_postlikes_proto_rawDescGZIP(), []int{5}
}

func (x *GetPostLikeCountResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type DeletePostLikeByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PostId        int64                  `protobuf:"varint,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePostLikeByIdRequest) Reset() {
	*x = DeletePostLikeByIdRequest{}
	mi := &file_content_v1_postlikes_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePostLikeByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePostLikeByIdRequest) ProtoMessage() {}

func (x *DeletePostLikeByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postlikes_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePostLikeByIdRequest.ProtoReflect.Descriptor instead.
func (*DeletePostLikeByIdRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_postlikes_proto_rawDescGZIP(), []int{6}
}

func (x *DeletePostLikeByIdRequest) GetPostId() int64 {
	if x != nil {
		return x.PostId
	}
	return 0
}

type DeletePostLikeByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePostLikeByIdResponse) Reset() {
	*x = DeletePostLikeByIdResponse{}
	mi := &file_content_v1_postlikes_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePostLikeByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePostLikeByIdResponse) ProtoMessage() {}

func (x *DeletePostLikeByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postlikes_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePostLikeByIdResponse.ProtoReflect.Descriptor instead.
func (*DeletePostLikeByIdResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_postlikes_proto_rawDescGZIP(), []int{7}
}

func (x *DeletePostLikeByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeletePostLikeByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type AddPostLikeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddPostLikeResponse) Reset() {
	*x = AddPostLikeResponse{}
	mi := &file_content_v1_postlikes_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPostLikeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPostLikeResponse) ProtoMessage() {}

func (x *AddPostLikeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postlikes_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPostLikeResponse.ProtoReflect.Descriptor instead.
func (*AddPostLikeResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_postlikes_proto_rawDescGZIP(), []int{8}
}

func (x *AddPostLikeResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *AddPostLikeResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_content_v1_postlikes_proto protoreflect.FileDescriptor

const file_content_v1_postlikes_proto_rawDesc = "" +
	"\n" +
	"\x1acontent/v1/postlikes.proto\x12\x0eapi.content.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"-\n" +
	"\x12AddPostLikeRequest\x12\x17\n" +
	"\apost_id\x18\x01 \x01(\x03R\x06postId\"2\n" +
	"\x17GetPostLikeCountRequest\x12\x17\n" +
	"\apost_id\x18\x01 \x01(\x03R\x06postId\"s\n" +
	"\x1bGetAllPostLikeByUserRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\x9d\x01\n" +
	"\x1cGetAllPostLikeByUserResponse\x12,\n" +
	"\x04data\x18\x01 \x03(\v2\x18.api.content.v1.PostLikeR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"w\n" +
	"\bPostLike\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x03R\x06postId\x129\n" +
	"\n" +
	"created_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"0\n" +
	"\x18GetPostLikeCountResponse\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x03R\x05count\"4\n" +
	"\x19DeletePostLikeByIdRequest\x12\x17\n" +
	"\apost_id\x18\x01 \x01(\x03R\x06postId\"P\n" +
	"\x1aDeletePostLikeByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"I\n" +
	"\x13AddPostLikeResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\xce\x03\n" +
	"\x0fPostLikeService\x12^\n" +
	"\vAddPostLike\x12\".api.content.v1.AddPostLikeRequest\x1a#.api.content.v1.AddPostLikeResponse\"\x06\x82\xb5\x18\x02\b\x01\x12m\n" +
	"\x10GetPostLikeCount\x12'.api.content.v1.GetPostLikeCountRequest\x1a(.api.content.v1.GetPostLikeCountResponse\"\x06\x82\xb5\x18\x02\b\x01\x12w\n" +
	"\x12GetPostLikesOfUser\x12+.api.content.v1.GetAllPostLikeByUserRequest\x1a,.api.content.v1.GetAllPostLikeByUserResponse\"\x06\x82\xb5\x18\x02\b\x01\x12s\n" +
	"\x12DeletePostLikeById\x12).api.content.v1.DeletePostLikeByIdRequest\x1a*.api.content.v1.DeletePostLikeByIdResponse\"\x06\x82\xb5\x18\x02\b\x01B4Z2github.com/nsp-inc/vtuber/api/content/v1;contentv1b\x06proto3"

var (
	file_content_v1_postlikes_proto_rawDescOnce sync.Once
	file_content_v1_postlikes_proto_rawDescData []byte
)

func file_content_v1_postlikes_proto_rawDescGZIP() []byte {
	file_content_v1_postlikes_proto_rawDescOnce.Do(func() {
		file_content_v1_postlikes_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_content_v1_postlikes_proto_rawDesc), len(file_content_v1_postlikes_proto_rawDesc)))
	})
	return file_content_v1_postlikes_proto_rawDescData
}

var file_content_v1_postlikes_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_content_v1_postlikes_proto_goTypes = []any{
	(*AddPostLikeRequest)(nil),           // 0: api.content.v1.AddPostLikeRequest
	(*GetPostLikeCountRequest)(nil),      // 1: api.content.v1.GetPostLikeCountRequest
	(*GetAllPostLikeByUserRequest)(nil),  // 2: api.content.v1.GetAllPostLikeByUserRequest
	(*GetAllPostLikeByUserResponse)(nil), // 3: api.content.v1.GetAllPostLikeByUserResponse
	(*PostLike)(nil),                     // 4: api.content.v1.PostLike
	(*GetPostLikeCountResponse)(nil),     // 5: api.content.v1.GetPostLikeCountResponse
	(*DeletePostLikeByIdRequest)(nil),    // 6: api.content.v1.DeletePostLikeByIdRequest
	(*DeletePostLikeByIdResponse)(nil),   // 7: api.content.v1.DeletePostLikeByIdResponse
	(*AddPostLikeResponse)(nil),          // 8: api.content.v1.AddPostLikeResponse
	(*v1.PaginationRequest)(nil),         // 9: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),         // 10: api.shared.v1.PaginationDetails
	(*timestamppb.Timestamp)(nil),        // 11: google.protobuf.Timestamp
}
var file_content_v1_postlikes_proto_depIdxs = []int32{
	9,  // 0: api.content.v1.GetAllPostLikeByUserRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	4,  // 1: api.content.v1.GetAllPostLikeByUserResponse.data:type_name -> api.content.v1.PostLike
	10, // 2: api.content.v1.GetAllPostLikeByUserResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	11, // 3: api.content.v1.PostLike.created_at:type_name -> google.protobuf.Timestamp
	0,  // 4: api.content.v1.PostLikeService.AddPostLike:input_type -> api.content.v1.AddPostLikeRequest
	1,  // 5: api.content.v1.PostLikeService.GetPostLikeCount:input_type -> api.content.v1.GetPostLikeCountRequest
	2,  // 6: api.content.v1.PostLikeService.GetPostLikesOfUser:input_type -> api.content.v1.GetAllPostLikeByUserRequest
	6,  // 7: api.content.v1.PostLikeService.DeletePostLikeById:input_type -> api.content.v1.DeletePostLikeByIdRequest
	8,  // 8: api.content.v1.PostLikeService.AddPostLike:output_type -> api.content.v1.AddPostLikeResponse
	5,  // 9: api.content.v1.PostLikeService.GetPostLikeCount:output_type -> api.content.v1.GetPostLikeCountResponse
	3,  // 10: api.content.v1.PostLikeService.GetPostLikesOfUser:output_type -> api.content.v1.GetAllPostLikeByUserResponse
	7,  // 11: api.content.v1.PostLikeService.DeletePostLikeById:output_type -> api.content.v1.DeletePostLikeByIdResponse
	8,  // [8:12] is the sub-list for method output_type
	4,  // [4:8] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_content_v1_postlikes_proto_init() }
func file_content_v1_postlikes_proto_init() {
	if File_content_v1_postlikes_proto != nil {
		return
	}
	file_content_v1_postlikes_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_content_v1_postlikes_proto_rawDesc), len(file_content_v1_postlikes_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_content_v1_postlikes_proto_goTypes,
		DependencyIndexes: file_content_v1_postlikes_proto_depIdxs,
		MessageInfos:      file_content_v1_postlikes_proto_msgTypes,
	}.Build()
	File_content_v1_postlikes_proto = out.File
	file_content_v1_postlikes_proto_goTypes = nil
	file_content_v1_postlikes_proto_depIdxs = nil
}
