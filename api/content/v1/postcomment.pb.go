// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: content/v1/postcomment.proto

package contentv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddPostCommentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty" validate:"required"`               
	PostId        int64                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty" validate:"required"`  
	ParentId      *int64                 `protobuf:"varint,4,opt,name=parent_id,json=parentId,proto3,oneof" json:"parent_id,omitempty"`
	AsVtuber      *bool                  `protobuf:"varint,5,opt,name=as_vtuber,json=asVtuber,proto3,oneof" json:"as_vtuber,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddPostCommentRequest) Reset() {
	*x = AddPostCommentRequest{}
	mi := &file_content_v1_postcomment_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPostCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPostCommentRequest) ProtoMessage() {}

func (x *AddPostCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPostCommentRequest.ProtoReflect.Descriptor instead.
func (*AddPostCommentRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{0}
}

func (x *AddPostCommentRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AddPostCommentRequest) GetPostId() int64 {
	if x != nil {
		return x.PostId
	}
	return 0
}

func (x *AddPostCommentRequest) GetParentId() int64 {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return 0
}

func (x *AddPostCommentRequest) GetAsVtuber() bool {
	if x != nil && x.AsVtuber != nil {
		return *x.AsVtuber
	}
	return false
}

type AddPostCommentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *PostComment           `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddPostCommentResponse) Reset() {
	*x = AddPostCommentResponse{}
	mi := &file_content_v1_postcomment_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPostCommentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPostCommentResponse) ProtoMessage() {}

func (x *AddPostCommentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPostCommentResponse.ProtoReflect.Descriptor instead.
func (*AddPostCommentResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{1}
}

func (x *AddPostCommentResponse) GetData() *PostComment {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetAllPostCommentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PostId        string                 `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty" validate:"required"`  
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllPostCommentsRequest) Reset() {
	*x = GetAllPostCommentsRequest{}
	mi := &file_content_v1_postcomment_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllPostCommentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllPostCommentsRequest) ProtoMessage() {}

func (x *GetAllPostCommentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllPostCommentsRequest.ProtoReflect.Descriptor instead.
func (*GetAllPostCommentsRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{2}
}

func (x *GetAllPostCommentsRequest) GetPostId() string {
	if x != nil {
		return x.PostId
	}
	return ""
}

func (x *GetAllPostCommentsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetAllPostCommentsResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*PostComment         `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllPostCommentsResponse) Reset() {
	*x = GetAllPostCommentsResponse{}
	mi := &file_content_v1_postcomment_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllPostCommentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllPostCommentsResponse) ProtoMessage() {}

func (x *GetAllPostCommentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllPostCommentsResponse.ProtoReflect.Descriptor instead.
func (*GetAllPostCommentsResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{3}
}

func (x *GetAllPostCommentsResponse) GetData() []*PostComment {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllPostCommentsResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type PostComment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	PostId        int64                  `protobuf:"varint,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ParentId      *int64                 `protobuf:"varint,5,opt,name=parent_id,json=parentId,proto3,oneof" json:"parent_id,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	HasReply      bool                   `protobuf:"varint,7,opt,name=has_reply,json=hasReply,proto3" json:"has_reply,omitempty"`
	User          *v1.Profile            `protobuf:"bytes,8,opt,name=user,proto3" json:"user,omitempty"`
	Vtuber        *v1.Profile            `protobuf:"bytes,9,opt,name=vtuber,proto3,oneof" json:"vtuber,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PostComment) Reset() {
	*x = PostComment{}
	mi := &file_content_v1_postcomment_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostComment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostComment) ProtoMessage() {}

func (x *PostComment) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostComment.ProtoReflect.Descriptor instead.
func (*PostComment) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{4}
}

func (x *PostComment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PostComment) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *PostComment) GetPostId() int64 {
	if x != nil {
		return x.PostId
	}
	return 0
}

func (x *PostComment) GetParentId() int64 {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return 0
}

func (x *PostComment) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PostComment) GetHasReply() bool {
	if x != nil {
		return x.HasReply
	}
	return false
}

func (x *PostComment) GetUser() *v1.Profile {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *PostComment) GetVtuber() *v1.Profile {
	if x != nil {
		return x.Vtuber
	}
	return nil
}

type GetPostCommentByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPostCommentByIdRequest) Reset() {
	*x = GetPostCommentByIdRequest{}
	mi := &file_content_v1_postcomment_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPostCommentByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostCommentByIdRequest) ProtoMessage() {}

func (x *GetPostCommentByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostCommentByIdRequest.ProtoReflect.Descriptor instead.
func (*GetPostCommentByIdRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{5}
}

func (x *GetPostCommentByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetPostRepliesOfCommentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPostRepliesOfCommentRequest) Reset() {
	*x = GetPostRepliesOfCommentRequest{}
	mi := &file_content_v1_postcomment_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPostRepliesOfCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostRepliesOfCommentRequest) ProtoMessage() {}

func (x *GetPostRepliesOfCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostRepliesOfCommentRequest.ProtoReflect.Descriptor instead.
func (*GetPostRepliesOfCommentRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{6}
}

func (x *GetPostRepliesOfCommentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPostRepliesOfCommentRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetPostCommentByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *PostComment           `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPostCommentByIdResponse) Reset() {
	*x = GetPostCommentByIdResponse{}
	mi := &file_content_v1_postcomment_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPostCommentByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostCommentByIdResponse) ProtoMessage() {}

func (x *GetPostCommentByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostCommentByIdResponse.ProtoReflect.Descriptor instead.
func (*GetPostCommentByIdResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{7}
}

func (x *GetPostCommentByIdResponse) GetData() *PostComment {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeletePostCommentByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePostCommentByIdRequest) Reset() {
	*x = DeletePostCommentByIdRequest{}
	mi := &file_content_v1_postcomment_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePostCommentByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePostCommentByIdRequest) ProtoMessage() {}

func (x *DeletePostCommentByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePostCommentByIdRequest.ProtoReflect.Descriptor instead.
func (*DeletePostCommentByIdRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{8}
}

func (x *DeletePostCommentByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdatePostCommentByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty" validate:"required"`  
	Id            int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty" validate:"required"`           
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePostCommentByIdRequest) Reset() {
	*x = UpdatePostCommentByIdRequest{}
	mi := &file_content_v1_postcomment_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePostCommentByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePostCommentByIdRequest) ProtoMessage() {}

func (x *UpdatePostCommentByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePostCommentByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdatePostCommentByIdRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{9}
}

func (x *UpdatePostCommentByIdRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UpdatePostCommentByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeletePostCommentByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePostCommentByIdResponse) Reset() {
	*x = DeletePostCommentByIdResponse{}
	mi := &file_content_v1_postcomment_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePostCommentByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePostCommentByIdResponse) ProtoMessage() {}

func (x *DeletePostCommentByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePostCommentByIdResponse.ProtoReflect.Descriptor instead.
func (*DeletePostCommentByIdResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{10}
}

func (x *DeletePostCommentByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeletePostCommentByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdatePostCommentByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePostCommentByIdResponse) Reset() {
	*x = UpdatePostCommentByIdResponse{}
	mi := &file_content_v1_postcomment_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePostCommentByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePostCommentByIdResponse) ProtoMessage() {}

func (x *UpdatePostCommentByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_postcomment_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePostCommentByIdResponse.ProtoReflect.Descriptor instead.
func (*UpdatePostCommentByIdResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_postcomment_proto_rawDescGZIP(), []int{11}
}

func (x *UpdatePostCommentByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdatePostCommentByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_content_v1_postcomment_proto protoreflect.FileDescriptor

const file_content_v1_postcomment_proto_rawDesc = "" +
	"\n" +
	"\x1ccontent/v1/postcomment.proto\x12\x0eapi.content.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"\xaa\x01\n" +
	"\x15AddPostCommentRequest\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x03R\x06postId\x12 \n" +
	"\tparent_id\x18\x04 \x01(\x03H\x00R\bparentId\x88\x01\x01\x12 \n" +
	"\tas_vtuber\x18\x05 \x01(\bH\x01R\basVtuber\x88\x01\x01B\f\n" +
	"\n" +
	"_parent_idB\f\n" +
	"\n" +
	"_as_vtuber\"I\n" +
	"\x16AddPostCommentResponse\x12/\n" +
	"\x04data\x18\x01 \x01(\v2\x1b.api.content.v1.PostCommentR\x04data\"\x8a\x01\n" +
	"\x19GetAllPostCommentsRequest\x12\x17\n" +
	"\apost_id\x18\x01 \x01(\tR\x06postId\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\x9e\x01\n" +
	"\x1aGetAllPostCommentsResponse\x12/\n" +
	"\x04data\x18\x01 \x03(\v2\x1b.api.content.v1.PostCommentR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\xc4\x02\n" +
	"\vPostComment\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x17\n" +
	"\apost_id\x18\x03 \x01(\x03R\x06postId\x12 \n" +
	"\tparent_id\x18\x05 \x01(\x03H\x00R\bparentId\x88\x01\x01\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1b\n" +
	"\thas_reply\x18\a \x01(\bR\bhasReply\x12*\n" +
	"\x04user\x18\b \x01(\v2\x16.api.shared.v1.ProfileR\x04user\x123\n" +
	"\x06vtuber\x18\t \x01(\v2\x16.api.shared.v1.ProfileH\x01R\x06vtuber\x88\x01\x01B\f\n" +
	"\n" +
	"_parent_idB\t\n" +
	"\a_vtuber\"+\n" +
	"\x19GetPostCommentByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x86\x01\n" +
	"\x1eGetPostRepliesOfCommentRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"M\n" +
	"\x1aGetPostCommentByIdResponse\x12/\n" +
	"\x04data\x18\x01 \x01(\v2\x1b.api.content.v1.PostCommentR\x04data\".\n" +
	"\x1cDeletePostCommentByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"H\n" +
	"\x1cUpdatePostCommentByIdRequest\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\"S\n" +
	"\x1dDeletePostCommentByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"S\n" +
	"\x1dUpdatePostCommentByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\x8c\x06\n" +
	"\x12PostCommentService\x12\x92\x01\n" +
	"\x0eAddPostComment\x12%.api.content.v1.AddPostCommentRequest\x1a&.api.content.v1.AddPostCommentResponse\"1\x82\xb5\x18-\b\x01\")as_vtuber==true?_user.vtuberId!=null:true\x12s\n" +
	"\x12GetAllPostComments\x12).api.content.v1.GetAllPostCommentsRequest\x1a*.api.content.v1.GetAllPostCommentsResponse\"\x06\x82\xb5\x18\x02\b\x01\x12{\n" +
	"\x15GetAllReplesOfComment\x12..api.content.v1.GetPostRepliesOfCommentRequest\x1a*.api.content.v1.GetAllPostCommentsResponse\"\x06\x82\xb5\x18\x02\b\x01\x12s\n" +
	"\x12GetPostCommentById\x12).api.content.v1.GetPostCommentByIdRequest\x1a*.api.content.v1.GetPostCommentByIdResponse\"\x06\x82\xb5\x18\x02\b\x01\x12|\n" +
	"\x15DeletePostCommentById\x12,.api.content.v1.DeletePostCommentByIdRequest\x1a-.api.content.v1.DeletePostCommentByIdResponse\"\x06\x82\xb5\x18\x02\b\x01\x12|\n" +
	"\x15UpdatePostCommentById\x12,.api.content.v1.UpdatePostCommentByIdRequest\x1a-.api.content.v1.UpdatePostCommentByIdResponse\"\x06\x82\xb5\x18\x02\b\x01B4Z2github.com/nsp-inc/vtuber/api/content/v1;contentv1b\x06proto3"

var (
	file_content_v1_postcomment_proto_rawDescOnce sync.Once
	file_content_v1_postcomment_proto_rawDescData []byte
)

func file_content_v1_postcomment_proto_rawDescGZIP() []byte {
	file_content_v1_postcomment_proto_rawDescOnce.Do(func() {
		file_content_v1_postcomment_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_content_v1_postcomment_proto_rawDesc), len(file_content_v1_postcomment_proto_rawDesc)))
	})
	return file_content_v1_postcomment_proto_rawDescData
}

var file_content_v1_postcomment_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_content_v1_postcomment_proto_goTypes = []any{
	(*AddPostCommentRequest)(nil),          // 0: api.content.v1.AddPostCommentRequest
	(*AddPostCommentResponse)(nil),         // 1: api.content.v1.AddPostCommentResponse
	(*GetAllPostCommentsRequest)(nil),      // 2: api.content.v1.GetAllPostCommentsRequest
	(*GetAllPostCommentsResponse)(nil),     // 3: api.content.v1.GetAllPostCommentsResponse
	(*PostComment)(nil),                    // 4: api.content.v1.PostComment
	(*GetPostCommentByIdRequest)(nil),      // 5: api.content.v1.GetPostCommentByIdRequest
	(*GetPostRepliesOfCommentRequest)(nil), // 6: api.content.v1.GetPostRepliesOfCommentRequest
	(*GetPostCommentByIdResponse)(nil),     // 7: api.content.v1.GetPostCommentByIdResponse
	(*DeletePostCommentByIdRequest)(nil),   // 8: api.content.v1.DeletePostCommentByIdRequest
	(*UpdatePostCommentByIdRequest)(nil),   // 9: api.content.v1.UpdatePostCommentByIdRequest
	(*DeletePostCommentByIdResponse)(nil),  // 10: api.content.v1.DeletePostCommentByIdResponse
	(*UpdatePostCommentByIdResponse)(nil),  // 11: api.content.v1.UpdatePostCommentByIdResponse
	(*v1.PaginationRequest)(nil),           // 12: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),           // 13: api.shared.v1.PaginationDetails
	(*timestamppb.Timestamp)(nil),          // 14: google.protobuf.Timestamp
	(*v1.Profile)(nil),                     // 15: api.shared.v1.Profile
}
var file_content_v1_postcomment_proto_depIdxs = []int32{
	4,  // 0: api.content.v1.AddPostCommentResponse.data:type_name -> api.content.v1.PostComment
	12, // 1: api.content.v1.GetAllPostCommentsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	4,  // 2: api.content.v1.GetAllPostCommentsResponse.data:type_name -> api.content.v1.PostComment
	13, // 3: api.content.v1.GetAllPostCommentsResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	14, // 4: api.content.v1.PostComment.created_at:type_name -> google.protobuf.Timestamp
	15, // 5: api.content.v1.PostComment.user:type_name -> api.shared.v1.Profile
	15, // 6: api.content.v1.PostComment.vtuber:type_name -> api.shared.v1.Profile
	12, // 7: api.content.v1.GetPostRepliesOfCommentRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	4,  // 8: api.content.v1.GetPostCommentByIdResponse.data:type_name -> api.content.v1.PostComment
	0,  // 9: api.content.v1.PostCommentService.AddPostComment:input_type -> api.content.v1.AddPostCommentRequest
	2,  // 10: api.content.v1.PostCommentService.GetAllPostComments:input_type -> api.content.v1.GetAllPostCommentsRequest
	6,  // 11: api.content.v1.PostCommentService.GetAllReplesOfComment:input_type -> api.content.v1.GetPostRepliesOfCommentRequest
	5,  // 12: api.content.v1.PostCommentService.GetPostCommentById:input_type -> api.content.v1.GetPostCommentByIdRequest
	8,  // 13: api.content.v1.PostCommentService.DeletePostCommentById:input_type -> api.content.v1.DeletePostCommentByIdRequest
	9,  // 14: api.content.v1.PostCommentService.UpdatePostCommentById:input_type -> api.content.v1.UpdatePostCommentByIdRequest
	1,  // 15: api.content.v1.PostCommentService.AddPostComment:output_type -> api.content.v1.AddPostCommentResponse
	3,  // 16: api.content.v1.PostCommentService.GetAllPostComments:output_type -> api.content.v1.GetAllPostCommentsResponse
	3,  // 17: api.content.v1.PostCommentService.GetAllReplesOfComment:output_type -> api.content.v1.GetAllPostCommentsResponse
	7,  // 18: api.content.v1.PostCommentService.GetPostCommentById:output_type -> api.content.v1.GetPostCommentByIdResponse
	10, // 19: api.content.v1.PostCommentService.DeletePostCommentById:output_type -> api.content.v1.DeletePostCommentByIdResponse
	11, // 20: api.content.v1.PostCommentService.UpdatePostCommentById:output_type -> api.content.v1.UpdatePostCommentByIdResponse
	15, // [15:21] is the sub-list for method output_type
	9,  // [9:15] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_content_v1_postcomment_proto_init() }
func file_content_v1_postcomment_proto_init() {
	if File_content_v1_postcomment_proto != nil {
		return
	}
	file_content_v1_postcomment_proto_msgTypes[0].OneofWrappers = []any{}
	file_content_v1_postcomment_proto_msgTypes[2].OneofWrappers = []any{}
	file_content_v1_postcomment_proto_msgTypes[4].OneofWrappers = []any{}
	file_content_v1_postcomment_proto_msgTypes[6].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_content_v1_postcomment_proto_rawDesc), len(file_content_v1_postcomment_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_content_v1_postcomment_proto_goTypes,
		DependencyIndexes: file_content_v1_postcomment_proto_depIdxs,
		MessageInfos:      file_content_v1_postcomment_proto_msgTypes,
	}.Build()
	File_content_v1_postcomment_proto = out.File
	file_content_v1_postcomment_proto_goTypes = nil
	file_content_v1_postcomment_proto_depIdxs = nil
}
